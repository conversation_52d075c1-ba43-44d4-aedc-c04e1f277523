# SimpleCrack - GUI Password Cracking Tool

A modern, cross-platform password cracking tool with a graphical user interface inspired by <PERSON> the Ripper. SimpleCrack provides an intuitive interface for password security testing while maintaining the power and flexibility of command-line tools.

## ⚠️ Legal and Ethical Use Warning

**IMPORTANT**: This tool is designed for legitimate security testing purposes only. Users must:
- Only use this tool on systems they own or have explicit written permission to test
- Comply with all applicable local, state, and federal laws
- Use this tool responsibly and ethically
- Not use this tool for malicious purposes or unauthorized access

The developers of SimpleCrack are not responsible for any misuse of this software.

## Features

### Hash Format Support
- **Common Formats**: MD5, SHA-1, SHA-256, SHA-512, bcrypt, NTLM
- **Extended Formats**: MySQL hashes, ZIP file hashes
- **File Input**: Support for hash files and PWDUMP format
- **Hash Extraction**: Extract hashes from ZIP, RAR, and /etc/shadow files

### Cracking Modes
- **Wordlist Mode**: Dictionary attacks with rule-based mangling
- **Brute-Force Mode**: Systematic password generation
- **Hybrid Mode**: Combination of wordlist and brute-force techniques

### GUI Features
- **Modern Interface**: Clean, intuitive design with tooltips and help
- **Real-time Progress**: Progress bars, speed indicators, and time estimates
- **Session Management**: Save and resume cracking sessions
- **Results Export**: Export cracked passwords to various formats
- **Multi-threading**: Utilize multiple CPU cores for optimal performance
- **GPU Acceleration**: Optional GPU support for intensive hash types

### Additional Tools
- **Hash Identification**: Automatic hash type detection
- **Password Strength Testing**: Analyze password security
- **Custom Rules**: Define wordlist manipulation rules
- **Extensible Architecture**: Plugin system for new hash formats

## Installation

### Prerequisites
- Python 3.8 or higher
- pip package manager

### Dependencies
```bash
pip install -r requirements.txt
```

### Quick Start
```bash
git clone https://github.com/yourusername/SimpleCrack.git
cd SimpleCrack
pip install -r requirements.txt
python main.py
```

## Usage

1. **Launch the Application**
   ```bash
   python main.py
   ```

2. **Input Hashes**
   - Paste hashes directly into the text box
   - Upload hash files using the file picker
   - Extract hashes from supported file formats

3. **Configure Cracking**
   - Select hash type from dropdown
   - Choose cracking mode (Wordlist/Brute-force/Hybrid)
   - Set parameters (character sets, rules, thread count)

4. **Start Cracking**
   - Click "Start" to begin the process
   - Monitor progress in real-time
   - View results as passwords are cracked

5. **Export Results**
   - Save cracked passwords to file
   - Export session data for later analysis

## Project Structure

```
SimpleCrack/
├── main.py                 # Application entry point
├── gui/                    # GUI components
│   ├── __init__.py
│   ├── main_window.py      # Main application window
│   ├── dialogs.py          # Dialog windows
│   └── widgets.py          # Custom GUI widgets
├── core/                   # Core cracking engine
│   ├── __init__.py
│   ├── hash_engine.py      # Hash processing
│   ├── crackers.py         # Cracking algorithms
│   └── utils.py            # Utility functions
├── plugins/                # Hash format plugins
│   ├── __init__.py
│   └── hash_formats/       # Individual hash format modules
├── data/                   # Sample data and wordlists
│   ├── wordlists/
│   └── test_hashes/
├── tests/                  # Unit tests
├── docs/                   # Documentation
├── requirements.txt        # Python dependencies
├── .gitignore             # Git ignore file
└── README.md              # This file
```

## Development

### Setting up Development Environment
```bash
git clone https://github.com/yourusername/SimpleCrack.git
cd SimpleCrack
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### Running Tests
```bash
python -m pytest tests/
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## Cross-Platform Support

SimpleCrack is designed to work on:
- **Linux**: All major distributions
- **Windows**: Windows 10/11
- **macOS**: macOS 10.14+

## Performance Notes

- Multi-threading is enabled by default for optimal CPU utilization
- GPU acceleration requires PyOpenCL and compatible hardware
- Memory usage scales with hash file size and wordlist size
- Performance varies significantly based on hash type complexity

## Security Considerations

- No default wordlists are included to prevent misuse
- Session files are stored locally and not transmitted
- Hash data is processed in memory and not logged to disk
- Users must provide their own wordlists and test data

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by John the Ripper password cracker
- Built with Python and Tkinter for cross-platform compatibility
- Thanks to the security research community for hash format specifications

## Support

For issues, questions, or contributions:
- GitHub Issues: [Report bugs or request features]
- Documentation: See the `docs/` directory
- Community: Follow ethical hacking guidelines and best practices

---

**Remember**: Use this tool responsibly and only for legitimate security testing purposes.
