"""
ZIP Hash Format Plugin

This module provides support for cracking ZIP file password hashes.
"""

import hashlib
import logging
import struct
import zipfile
from typing import Optional, Dict, Any
from pathlib import Path

from core.hash_engine import HashProcessor


class ZipHashProcessor(HashProcessor):
    """ZIP file hash processor for password cracking."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.zip_data: Optional[Dict[str, Any]] = None
    
    def extract_hash_from_zip(self, zip_path: str) -> Optional[str]:
        """
        Extract hash information from a ZIP file.
        
        Args:
            zip_path: Path to the ZIP file
            
        Returns:
            Hash string that can be used for cracking
        """
        try:
            with zipfile.ZipFile(zip_path, 'r') as zf:
                # Get the first encrypted file
                for info in zf.infolist():
                    if info.flag_bits & 0x1:  # Check if encrypted
                        # Read the local file header
                        with open(zip_path, 'rb') as f:
                            # Find the local file header
                            f.seek(info.header_offset)
                            header = f.read(30)  # Local file header size
                            
                            if len(header) < 30:
                                continue
                            
                            # Parse local file header
                            signature, version, flags, method, mod_time, mod_date, crc32, comp_size, uncomp_size, name_len, extra_len = struct.unpack('<LHHHHHLLLHH', header)
                            
                            if signature != 0x04034b50:  # Local file header signature
                                continue
                            
                            # Skip filename and extra field
                            f.seek(name_len + extra_len, 1)
                            
                            # Read the first 12 bytes of encrypted data
                            encrypted_data = f.read(12)
                            
                            if len(encrypted_data) < 12:
                                continue
                            
                            # Store ZIP data for verification
                            self.zip_data = {
                                'crc32': crc32,
                                'encrypted_header': encrypted_data,
                                'mod_time': mod_time,
                                'zip_path': zip_path,
                                'filename': info.filename
                            }
                            
                            # Create a hash string that includes the necessary information
                            hash_string = f"$zip$*{method}*{flags}*{comp_size}*{uncomp_size}*{crc32:08x}*{encrypted_data.hex()}*{info.filename}"
                            
                            return hash_string
                
                self.logger.warning(f"No encrypted files found in ZIP: {zip_path}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error extracting hash from ZIP {zip_path}: {e}")
            return None
    
    def hash_password(self, password: str) -> str:
        """
        This method is not applicable for ZIP hashes as they are extracted from files.
        """
        raise NotImplementedError("ZIP hashes are extracted from files, not generated from passwords")
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """
        Verify a password against a ZIP hash.
        
        Args:
            password: Password to test
            hash_value: ZIP hash string
            
        Returns:
            True if password is correct
        """
        try:
            # Parse the hash string
            if not hash_value.startswith('$zip$'):
                return False
            
            parts = hash_value.split('*')
            if len(parts) < 8:
                return False
            
            method = int(parts[1])
            flags = int(parts[2])
            comp_size = int(parts[3])
            uncomp_size = int(parts[4])
            crc32 = int(parts[5], 16)
            encrypted_header = bytes.fromhex(parts[6])
            filename = parts[7]
            
            # Implement ZIP password verification
            # This is a simplified version - real implementation would need
            # to handle different ZIP encryption methods
            
            if method == 0:  # Traditional ZIP encryption
                return self._verify_traditional_zip(password, crc32, encrypted_header)
            else:
                self.logger.warning(f"Unsupported ZIP encryption method: {method}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error verifying ZIP password: {e}")
            return False
    
    def _verify_traditional_zip(self, password: str, crc32: int, encrypted_header: bytes) -> bool:
        """
        Verify password using traditional ZIP encryption.
        
        This is a simplified implementation. A complete implementation would
        require the full ZIP decryption algorithm.
        """
        try:
            # Initialize the three 32-bit keys
            key0 = 0x12345678
            key1 = 0x23456789
            key2 = 0x34567890
            
            # Update keys with password
            for char in password.encode('latin-1'):
                key0 = self._crc32_update(key0, char)
                key1 = (key1 + (key0 & 0xff)) & 0xffffffff
                key1 = (key1 * 134775813 + 1) & 0xffffffff
                key2 = self._crc32_update(key2, key1 >> 24)
            
            # Decrypt the first 12 bytes
            decrypted = bytearray()
            for i in range(12):
                temp = key2 | 2
                byte = encrypted_header[i] ^ ((temp * (temp ^ 1)) >> 8) & 0xff
                decrypted.append(byte)
                
                # Update keys
                key0 = self._crc32_update(key0, byte)
                key1 = (key1 + (key0 & 0xff)) & 0xffffffff
                key1 = (key1 * 134775813 + 1) & 0xffffffff
                key2 = self._crc32_update(key2, key1 >> 24)
            
            # Check if the last byte matches the high byte of CRC32
            # This is a simplified check
            return (decrypted[11] == ((crc32 >> 24) & 0xff))
            
        except Exception as e:
            self.logger.error(f"Error in traditional ZIP verification: {e}")
            return False
    
    def _crc32_update(self, crc: int, byte: int) -> int:
        """Update CRC32 value with a byte."""
        # Simplified CRC32 update - in practice, you'd use a lookup table
        crc ^= byte
        for _ in range(8):
            if crc & 1:
                crc = (crc >> 1) ^ 0xedb88320
            else:
                crc >>= 1
        return crc & 0xffffffff
    
    def get_name(self) -> str:
        return "ZIP"
    
    def get_description(self) -> str:
        return "ZIP file password hash (Traditional encryption)"


def extract_zip_hashes(zip_path: str) -> Dict[str, str]:
    """
    Extract all password hashes from a ZIP file.
    
    Args:
        zip_path: Path to the ZIP file
        
    Returns:
        Dictionary mapping filenames to hash strings
    """
    processor = ZipHashProcessor()
    hashes = {}
    
    try:
        with zipfile.ZipFile(zip_path, 'r') as zf:
            for info in zf.infolist():
                if info.flag_bits & 0x1:  # Encrypted file
                    hash_value = processor.extract_hash_from_zip(zip_path)
                    if hash_value:
                        hashes[info.filename] = hash_value
                        break  # For now, just handle the first encrypted file
    
    except Exception as e:
        logging.getLogger(__name__).error(f"Error extracting ZIP hashes: {e}")
    
    return hashes


# Register the processor
def get_processor():
    """Get the ZIP hash processor instance."""
    return ZipHashProcessor()
