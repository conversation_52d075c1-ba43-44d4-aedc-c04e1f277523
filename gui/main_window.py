"""
SimpleCrack Main GUI Window

This module contains the main application window and GUI components.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any

from core.hash_engine import HashEngine
from core.crackers import C<PERSON>ingEngine, WordlistGenerator, BruteForceGenerator, HybridGenerator
from core.hash_extractor import HashExtractor, HashIdentifier
from core.utils import format_time_duration, format_hash_rate, get_system_info
from .widgets import ProgressFrame, ResultsFrame, ConfigFrame
from .dialogs import AboutDialog, HelpDialog, SettingsDialog


class SimpleCrackGUI:
    """Main GUI application class."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Initialize core components
        self.hash_engine = HashEngine()
        self.cracking_engine = CrackingEngine(self.hash_engine)
        self.hash_extractor = HashExtractor()
        self.hash_identifier = HashIdentifier()
        
        # GUI state
        self.current_session = None
        self.is_cracking = False
        self.is_paused = False
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("SimpleCrack - Password Cracking Tool")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Set up GUI
        self._setup_styles()
        self._create_menu()
        self._create_main_interface()
        self._setup_callbacks()
        
        self.logger.info("GUI initialized successfully")
    
    def _setup_styles(self):
        """Set up GUI styles and themes."""
        style = ttk.Style()
        
        # Configure styles for better appearance
        style.configure('Title.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 10, 'bold'))
        style.configure('Status.TLabel', font=('Arial', 9))
        
        # Configure button styles
        style.configure('Action.TButton', font=('Arial', 10, 'bold'))
        style.configure('Danger.TButton', foreground='red')
        style.configure('Success.TButton', foreground='green')
    
    def _create_menu(self):
        """Create the application menu bar."""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="Load Hash File...", command=self._load_hash_file)
        file_menu.add_command(label="Load Wordlist...", command=self._load_wordlist)
        file_menu.add_separator()
        file_menu.add_command(label="Save Session...", command=self._save_session)
        file_menu.add_command(label="Load Session...", command=self._load_session)
        file_menu.add_separator()
        file_menu.add_command(label="Export Results...", command=self._export_results)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self._on_closing)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Hash Identifier", command=self._show_hash_identifier)
        tools_menu.add_command(label="Password Strength Test", command=self._show_password_strength)
        tools_menu.add_command(label="Extract Hashes", command=self._show_hash_extractor)
        tools_menu.add_separator()
        tools_menu.add_command(label="System Information", command=self._show_system_info)
        
        # Settings menu
        settings_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Settings", menu=settings_menu)
        settings_menu.add_command(label="Preferences...", command=self._show_settings)
        settings_menu.add_command(label="Performance...", command=self._show_performance_settings)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="User Guide", command=self._show_help)
        help_menu.add_command(label="Ethical Use Guidelines", command=self._show_ethics)
        help_menu.add_separator()
        help_menu.add_command(label="About SimpleCrack", command=self._show_about)
    
    def _create_main_interface(self):
        """Create the main interface components."""
        # Create main container with paned window
        main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel for input and configuration
        left_frame = ttk.Frame(main_paned)
        main_paned.add(left_frame, weight=1)
        
        # Right panel for progress and results
        right_frame = ttk.Frame(main_paned)
        main_paned.add(right_frame, weight=2)
        
        # Create left panel components
        self._create_input_section(left_frame)
        self._create_config_section(left_frame)
        self._create_control_section(left_frame)
        
        # Create right panel components
        self._create_progress_section(right_frame)
        self._create_results_section(right_frame)
        self._create_status_section(right_frame)
    
    def _create_input_section(self, parent):
        """Create the hash input section."""
        # Input section frame
        input_frame = ttk.LabelFrame(parent, text="Hash Input", padding=10)
        input_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 5))
        
        # Hash input methods
        input_method_frame = ttk.Frame(input_frame)
        input_method_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(input_method_frame, text="Input Method:").pack(side=tk.LEFT)
        
        self.input_method = tk.StringVar(value="text")
        ttk.Radiobutton(input_method_frame, text="Text Input", 
                       variable=self.input_method, value="text").pack(side=tk.LEFT, padx=(10, 5))
        ttk.Radiobutton(input_method_frame, text="File Upload", 
                       variable=self.input_method, value="file").pack(side=tk.LEFT, padx=5)
        
        # Text input area
        text_frame = ttk.Frame(input_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(text_frame, text="Hash Values (one per line):").pack(anchor=tk.W)
        
        self.hash_text = scrolledtext.ScrolledText(text_frame, height=8, wrap=tk.WORD)
        self.hash_text.pack(fill=tk.BOTH, expand=True, pady=(5, 10))
        
        # File input controls
        file_frame = ttk.Frame(input_frame)
        file_frame.pack(fill=tk.X)
        
        self.file_path = tk.StringVar()
        ttk.Label(file_frame, text="Hash File:").pack(side=tk.LEFT)
        ttk.Entry(file_frame, textvariable=self.file_path, state="readonly").pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(file_frame, text="Browse...", command=self._browse_hash_file).pack(side=tk.RIGHT)
        
        # Hash type detection
        detect_frame = ttk.Frame(input_frame)
        detect_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(detect_frame, text="Auto-Detect Hash Types", 
                  command=self._auto_detect_hashes).pack(side=tk.LEFT)
        ttk.Button(detect_frame, text="Extract from File", 
                  command=self._extract_hashes).pack(side=tk.LEFT, padx=(5, 0))
    
    def _create_config_section(self, parent):
        """Create the configuration section."""
        config_frame = ttk.LabelFrame(parent, text="Cracking Configuration", padding=10)
        config_frame.pack(fill=tk.X, pady=5)
        
        # Hash type selection
        hash_type_frame = ttk.Frame(config_frame)
        hash_type_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(hash_type_frame, text="Hash Type:").pack(side=tk.LEFT)
        
        self.hash_type = tk.StringVar()
        hash_types = self.hash_engine.get_available_processors()
        self.hash_type_combo = ttk.Combobox(hash_type_frame, textvariable=self.hash_type, 
                                           values=hash_types, state="readonly")
        self.hash_type_combo.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        
        if hash_types:
            self.hash_type_combo.set(hash_types[0])
        
        # Cracking mode selection
        mode_frame = ttk.Frame(config_frame)
        mode_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(mode_frame, text="Cracking Mode:").pack(side=tk.LEFT)
        
        self.crack_mode = tk.StringVar(value="wordlist")
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.crack_mode, 
                                 values=["wordlist", "brute_force", "hybrid"], state="readonly")
        mode_combo.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        mode_combo.bind('<<ComboboxSelected>>', self._on_mode_changed)
        
        # Mode-specific configuration frame
        self.mode_config_frame = ttk.Frame(config_frame)
        self.mode_config_frame.pack(fill=tk.X, pady=(0, 10))
        
        self._create_wordlist_config()
        
        # Performance settings
        perf_frame = ttk.Frame(config_frame)
        perf_frame.pack(fill=tk.X)
        
        ttk.Label(perf_frame, text="Threads:").pack(side=tk.LEFT)
        
        self.thread_count = tk.IntVar(value=4)
        thread_spin = ttk.Spinbox(perf_frame, from_=1, to=16, textvariable=self.thread_count, width=5)
        thread_spin.pack(side=tk.LEFT, padx=(5, 10))
        
        self.gpu_enabled = tk.BooleanVar()
        ttk.Checkbutton(perf_frame, text="Enable GPU Acceleration", 
                       variable=self.gpu_enabled).pack(side=tk.LEFT)
    
    def _create_wordlist_config(self):
        """Create wordlist mode configuration."""
        # Clear existing widgets
        for widget in self.mode_config_frame.winfo_children():
            widget.destroy()
        
        # Wordlist file selection
        wordlist_frame = ttk.Frame(self.mode_config_frame)
        wordlist_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(wordlist_frame, text="Wordlist:").pack(side=tk.LEFT)
        
        self.wordlist_path = tk.StringVar()
        ttk.Entry(wordlist_frame, textvariable=self.wordlist_path, state="readonly").pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(wordlist_frame, text="Browse...", command=self._browse_wordlist).pack(side=tk.RIGHT)
        
        # Rules options
        rules_frame = ttk.Frame(self.mode_config_frame)
        rules_frame.pack(fill=tk.X)
        
        self.apply_rules = tk.BooleanVar()
        ttk.Checkbutton(rules_frame, text="Apply common rules (case changes, numbers, etc.)", 
                       variable=self.apply_rules).pack(side=tk.LEFT)
    
    def _create_brute_force_config(self):
        """Create brute force mode configuration."""
        # Clear existing widgets
        for widget in self.mode_config_frame.winfo_children():
            widget.destroy()
        
        # Character set selection
        charset_frame = ttk.Frame(self.mode_config_frame)
        charset_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(charset_frame, text="Character Set:").pack(side=tk.LEFT)
        
        self.charset_type = tk.StringVar(value="lowercase")
        charset_combo = ttk.Combobox(charset_frame, textvariable=self.charset_type,
                                    values=["lowercase", "uppercase", "mixed_case", "alphanumeric", "all_printable"],
                                    state="readonly")
        charset_combo.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        
        # Length settings
        length_frame = ttk.Frame(self.mode_config_frame)
        length_frame.pack(fill=tk.X)
        
        ttk.Label(length_frame, text="Min Length:").pack(side=tk.LEFT)
        self.min_length = tk.IntVar(value=1)
        ttk.Spinbox(length_frame, from_=1, to=20, textvariable=self.min_length, width=5).pack(
            side=tk.LEFT, padx=(5, 10))
        
        ttk.Label(length_frame, text="Max Length:").pack(side=tk.LEFT)
        self.max_length = tk.IntVar(value=6)
        ttk.Spinbox(length_frame, from_=1, to=20, textvariable=self.max_length, width=5).pack(
            side=tk.LEFT, padx=(5, 0))
    
    def _create_hybrid_config(self):
        """Create hybrid mode configuration."""
        # Clear existing widgets
        for widget in self.mode_config_frame.winfo_children():
            widget.destroy()
        
        # Wordlist file selection
        wordlist_frame = ttk.Frame(self.mode_config_frame)
        wordlist_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(wordlist_frame, text="Base Wordlist:").pack(side=tk.LEFT)
        
        self.hybrid_wordlist_path = tk.StringVar()
        ttk.Entry(wordlist_frame, textvariable=self.hybrid_wordlist_path, state="readonly").pack(
            side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        ttk.Button(wordlist_frame, text="Browse...", command=self._browse_hybrid_wordlist).pack(side=tk.RIGHT)
        
        # Hybrid options
        options_frame = ttk.Frame(self.mode_config_frame)
        options_frame.pack(fill=tk.X)
        
        self.append_digits = tk.IntVar(value=2)
        ttk.Label(options_frame, text="Append digits:").pack(side=tk.LEFT)
        ttk.Spinbox(options_frame, from_=0, to=4, textvariable=self.append_digits, width=5).pack(
            side=tk.LEFT, padx=(5, 10))
        
        self.append_special = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Append special chars", 
                       variable=self.append_special).pack(side=tk.LEFT)
    
    def _create_control_section(self, parent):
        """Create the control buttons section."""
        control_frame = ttk.LabelFrame(parent, text="Controls", padding=10)
        control_frame.pack(fill=tk.X, pady=5)
        
        # Main control buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.start_button = ttk.Button(button_frame, text="Start Cracking", 
                                      command=self._start_cracking, style='Action.TButton')
        self.start_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.pause_button = ttk.Button(button_frame, text="Pause", 
                                      command=self._pause_cracking, state=tk.DISABLED)
        self.pause_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop", 
                                     command=self._stop_cracking, state=tk.DISABLED, 
                                     style='Danger.TButton')
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # Session controls
        session_frame = ttk.Frame(control_frame)
        session_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(session_frame, text="Save Session", command=self._save_session).pack(side=tk.LEFT)
        ttk.Button(session_frame, text="Load Session", command=self._load_session).pack(
            side=tk.LEFT, padx=(5, 0))
        ttk.Button(session_frame, text="Clear All", command=self._clear_all).pack(side=tk.RIGHT)

    def _create_progress_section(self, parent):
        """Create the progress monitoring section."""
        progress_frame = ttk.LabelFrame(parent, text="Progress", padding=10)
        progress_frame.pack(fill=tk.X, pady=(0, 5))

        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                           mode='determinate', length=400)
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # Progress statistics
        stats_frame = ttk.Frame(progress_frame)
        stats_frame.pack(fill=tk.X)

        # Left column
        left_stats = ttk.Frame(stats_frame)
        left_stats.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.attempts_label = ttk.Label(left_stats, text="Attempts: 0")
        self.attempts_label.pack(anchor=tk.W)

        self.speed_label = ttk.Label(left_stats, text="Speed: 0 H/s")
        self.speed_label.pack(anchor=tk.W)

        self.cracked_label = ttk.Label(left_stats, text="Cracked: 0/0")
        self.cracked_label.pack(anchor=tk.W)

        # Right column
        right_stats = ttk.Frame(stats_frame)
        right_stats.pack(side=tk.RIGHT, fill=tk.X, expand=True)

        self.elapsed_label = ttk.Label(right_stats, text="Elapsed: 00:00:00")
        self.elapsed_label.pack(anchor=tk.E)

        self.eta_label = ttk.Label(right_stats, text="ETA: Unknown")
        self.eta_label.pack(anchor=tk.E)

        self.efficiency_label = ttk.Label(right_stats, text="Efficiency: 0%")
        self.efficiency_label.pack(anchor=tk.E)

    def _create_results_section(self, parent):
        """Create the results display section."""
        results_frame = ttk.LabelFrame(parent, text="Results", padding=10)
        results_frame.pack(fill=tk.BOTH, expand=True, pady=5)

        # Results tree view
        columns = ('Hash', 'Password', 'Time', 'Status')
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)

        # Configure columns
        self.results_tree.heading('Hash', text='Hash (truncated)')
        self.results_tree.heading('Password', text='Password')
        self.results_tree.heading('Time', text='Time Taken')
        self.results_tree.heading('Status', text='Status')

        self.results_tree.column('Hash', width=200)
        self.results_tree.column('Password', width=150)
        self.results_tree.column('Time', width=100)
        self.results_tree.column('Status', width=100)

        # Scrollbars for results tree
        results_scroll_y = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        results_scroll_x = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(yscrollcommand=results_scroll_y.set, xscrollcommand=results_scroll_x.set)

        # Pack results tree and scrollbars
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scroll_y.pack(side=tk.RIGHT, fill=tk.Y)
        results_scroll_x.pack(side=tk.BOTTOM, fill=tk.X)

        # Results context menu
        self.results_menu = tk.Menu(self.root, tearoff=0)
        self.results_menu.add_command(label="Copy Hash", command=self._copy_hash)
        self.results_menu.add_command(label="Copy Password", command=self._copy_password)
        self.results_menu.add_separator()
        self.results_menu.add_command(label="Export Selected", command=self._export_selected)

        self.results_tree.bind("<Button-3>", self._show_results_menu)

    def _create_status_section(self, parent):
        """Create the status and log section."""
        status_frame = ttk.LabelFrame(parent, text="Status & Logs", padding=10)
        status_frame.pack(fill=tk.X, pady=(5, 0))

        # Status bar
        self.status_var = tk.StringVar(value="Ready")
        status_bar = ttk.Label(status_frame, textvariable=self.status_var, style='Status.TLabel')
        status_bar.pack(fill=tk.X, pady=(0, 5))

        # Log area
        self.log_text = scrolledtext.ScrolledText(status_frame, height=6, wrap=tk.WORD)
        self.log_text.pack(fill=tk.X)

        # Configure log text tags for different log levels
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")
        self.log_text.tag_configure("SUCCESS", foreground="green")

    def _setup_callbacks(self):
        """Set up event callbacks and handlers."""
        # Window closing handler
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)

        # Set up cracking engine callback
        self.cracking_engine.set_progress_callback(self._update_progress)

        # Set up logging handler for GUI
        self._setup_log_handler()

    def _setup_log_handler(self):
        """Set up logging handler to display logs in GUI."""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget, status_var):
                super().__init__()
                self.text_widget = text_widget
                self.status_var = status_var

            def emit(self, record):
                try:
                    msg = self.format(record)

                    # Update status bar with latest message
                    self.status_var.set(msg)

                    # Add to log text with appropriate tag
                    tag = record.levelname
                    if tag not in ["INFO", "WARNING", "ERROR"]:
                        tag = "INFO"

                    self.text_widget.insert(tk.END, msg + "\n", tag)
                    self.text_widget.see(tk.END)

                    # Limit log size
                    lines = int(self.text_widget.index('end-1c').split('.')[0])
                    if lines > 1000:
                        self.text_widget.delete('1.0', '500.0')

                except Exception:
                    pass  # Ignore errors in log handler

        # Add GUI log handler
        gui_handler = GUILogHandler(self.log_text, self.status_var)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logging.getLogger().addHandler(gui_handler)

    def _on_mode_changed(self, event=None):
        """Handle cracking mode change."""
        mode = self.crack_mode.get()

        if mode == "wordlist":
            self._create_wordlist_config()
        elif mode == "brute_force":
            self._create_brute_force_config()
        elif mode == "hybrid":
            self._create_hybrid_config()

    def _browse_hash_file(self):
        """Browse for hash file."""
        filename = filedialog.askopenfilename(
            title="Select Hash File",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.file_path.set(filename)

    def _browse_wordlist(self):
        """Browse for wordlist file."""
        filename = filedialog.askopenfilename(
            title="Select Wordlist File",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.wordlist_path.set(filename)

    def _browse_hybrid_wordlist(self):
        """Browse for hybrid wordlist file."""
        filename = filedialog.askopenfilename(
            title="Select Base Wordlist File",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.hybrid_wordlist_path.set(filename)

    def _load_hash_file(self):
        """Load hashes from file."""
        filename = filedialog.askopenfilename(
            title="Load Hash File",
            filetypes=[
                ("Text files", "*.txt"),
                ("Hash files", "*.hash"),
                ("All files", "*.*")
            ]
        )
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                self.hash_text.delete('1.0', tk.END)
                self.hash_text.insert('1.0', content)
                self.logger.info(f"Loaded hash file: {filename}")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load hash file:\n{e}")

    def _load_wordlist(self):
        """Load wordlist file."""
        filename = filedialog.askopenfilename(
            title="Load Wordlist File",
            filetypes=[
                ("Text files", "*.txt"),
                ("Wordlist files", "*.wordlist"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.wordlist_path.set(filename)
            self.logger.info(f"Selected wordlist: {filename}")

    def _auto_detect_hashes(self):
        """Auto-detect hash types from input."""
        hash_text = self.hash_text.get('1.0', tk.END).strip()
        if not hash_text:
            messagebox.showwarning("Warning", "Please enter some hashes first.")
            return

        # Extract hashes from text
        hashes = [line.strip() for line in hash_text.split('\n') if line.strip()]

        # Identify hash types
        categorized = self.hash_identifier.identify_multiple_hashes(hashes)

        if not categorized:
            messagebox.showinfo("No Hashes", "No recognizable hash formats found.")
            return

        # Show results
        result_text = "Detected hash types:\n\n"
        for hash_type, hash_list in categorized.items():
            result_text += f"{hash_type.upper()}: {len(hash_list)} hashes\n"

        # Auto-select the most common hash type
        most_common = max(categorized.keys(), key=lambda k: len(categorized[k]))
        if most_common in self.hash_engine.get_available_processors():
            self.hash_type.set(most_common)
            result_text += f"\nAuto-selected hash type: {most_common.upper()}"

        messagebox.showinfo("Hash Detection Results", result_text)

    def _extract_hashes(self):
        """Extract hashes from various file formats."""
        filename = filedialog.askopenfilename(
            title="Select File to Extract Hashes From",
            filetypes=[
                ("All supported", "*.zip;*.rar;*.txt;*.shadow;*.pwdump"),
                ("ZIP files", "*.zip"),
                ("RAR files", "*.rar"),
                ("Text files", "*.txt"),
                ("Shadow files", "*shadow*"),
                ("All files", "*.*")
            ]
        )

        if not filename:
            return

        try:
            extracted = self.hash_extractor.extract_from_file(filename)

            if not extracted:
                messagebox.showinfo("No Hashes", "No hashes found in the selected file.")
                return

            # Display extracted hashes
            result_text = ""
            for hash_type, hash_list in extracted.items():
                result_text += f"# {hash_type.upper()} hashes\n"
                for hash_value in hash_list:
                    result_text += f"{hash_value}\n"
                result_text += "\n"

            # Add to hash input area
            current_text = self.hash_text.get('1.0', tk.END).strip()
            if current_text:
                result_text = current_text + "\n\n" + result_text

            self.hash_text.delete('1.0', tk.END)
            self.hash_text.insert('1.0', result_text)

            self.logger.info(f"Extracted hashes from: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to extract hashes:\n{e}")

    def _start_cracking(self):
        """Start the password cracking process."""
        if self.is_cracking:
            return

        # Validate inputs
        if not self._validate_inputs():
            return

        # Get hashes to crack
        hashes = self._get_hashes_to_crack()
        if not hashes:
            messagebox.showwarning("Warning", "No valid hashes to crack.")
            return

        # Get password generator
        generator = self._get_password_generator()
        if not generator:
            return

        # Update UI state
        self.is_cracking = True
        self.is_paused = False
        self._update_control_buttons()

        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Add hashes to results tree
        for hash_value in hashes:
            self.results_tree.insert('', tk.END, values=(
                hash_value[:32] + "..." if len(hash_value) > 32 else hash_value,
                "",
                "",
                "Pending"
            ))

        # Start cracking in background thread
        self.cracking_thread = threading.Thread(
            target=self._crack_passwords_thread,
            args=(hashes, self.hash_type.get(), generator),
            daemon=True
        )
        self.cracking_thread.start()

        self.logger.info("Password cracking started")

    def _crack_passwords_thread(self, hashes, hash_type, generator):
        """Background thread for password cracking."""
        try:
            # Update thread count
            self.cracking_engine.num_threads = self.thread_count.get()

            # Start cracking
            results = self.cracking_engine.crack_hashes(hashes, hash_type, generator)

            # Update results in GUI thread
            self.root.after(0, self._cracking_completed, results)

        except Exception as e:
            self.logger.error(f"Error in cracking thread: {e}")
            self.root.after(0, self._cracking_error, str(e))

    def _cracking_completed(self, results):
        """Handle cracking completion."""
        self.is_cracking = False
        self._update_control_buttons()

        # Update results tree
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        for hash_value, result in results.items():
            status = "Cracked" if result.cracked else "Not Found"
            password = result.password if result.cracked else ""
            time_taken = format_time_duration(result.time_taken)

            self.results_tree.insert('', tk.END, values=(
                hash_value[:32] + "..." if len(hash_value) > 32 else hash_value,
                password,
                time_taken,
                status
            ))

        # Show completion message
        cracked_count = sum(1 for r in results.values() if r.cracked)
        total_count = len(results)

        self.logger.info(f"Cracking completed: {cracked_count}/{total_count} passwords found")

        if cracked_count > 0:
            messagebox.showinfo("Cracking Complete",
                              f"Successfully cracked {cracked_count} out of {total_count} passwords!")
        else:
            messagebox.showinfo("Cracking Complete",
                              "No passwords were cracked. Try a different wordlist or method.")

    def _cracking_error(self, error_msg):
        """Handle cracking error."""
        self.is_cracking = False
        self._update_control_buttons()
        messagebox.showerror("Cracking Error", f"An error occurred during cracking:\n{error_msg}")

    def _pause_cracking(self):
        """Pause/resume the cracking process."""
        if not self.is_cracking:
            return

        if self.is_paused:
            self.cracking_engine.resume()
            self.is_paused = False
            self.pause_button.config(text="Pause")
            self.logger.info("Cracking resumed")
        else:
            self.cracking_engine.pause()
            self.is_paused = True
            self.pause_button.config(text="Resume")
            self.logger.info("Cracking paused")

    def _stop_cracking(self):
        """Stop the cracking process."""
        if not self.is_cracking:
            return

        self.cracking_engine.stop()
        self.is_cracking = False
        self.is_paused = False
        self._update_control_buttons()
        self.logger.info("Cracking stopped by user")

    def _update_control_buttons(self):
        """Update the state of control buttons."""
        if self.is_cracking:
            self.start_button.config(state=tk.DISABLED)
            self.pause_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.NORMAL)
        else:
            self.start_button.config(state=tk.NORMAL)
            self.pause_button.config(state=tk.DISABLED, text="Pause")
            self.stop_button.config(state=tk.DISABLED)

    def _update_progress(self, progress_data):
        """Update progress display."""
        # This method is called from the cracking thread, so use after() to update GUI
        self.root.after(0, self._update_progress_gui, progress_data)

    def _update_progress_gui(self, progress_data):
        """Update progress display in GUI thread."""
        try:
            attempts = progress_data.get('total_attempts', 0)
            speed = progress_data.get('speed', 0)
            elapsed = progress_data.get('elapsed_time', 0)
            cracked = progress_data.get('cracked_count', 0)
            total = progress_data.get('total_hashes', 0)

            # Update labels
            self.attempts_label.config(text=f"Attempts: {attempts:,}")
            self.speed_label.config(text=f"Speed: {format_hash_rate(speed)}")
            self.cracked_label.config(text=f"Cracked: {cracked}/{total}")
            self.elapsed_label.config(text=f"Elapsed: {format_time_duration(elapsed)}")

            # Update progress bar (if we have an estimate)
            if total > 0:
                progress = (cracked / total) * 100
                self.progress_var.set(progress)

                # Calculate efficiency
                efficiency = (cracked / total) * 100 if total > 0 else 0
                self.efficiency_label.config(text=f"Efficiency: {efficiency:.1f}%")

        except Exception as e:
            self.logger.error(f"Error updating progress: {e}")

    def _validate_inputs(self):
        """Validate user inputs before starting cracking."""
        # Check hash type
        if not self.hash_type.get():
            messagebox.showerror("Error", "Please select a hash type.")
            return False

        # Check cracking mode configuration
        mode = self.crack_mode.get()

        if mode == "wordlist":
            if not self.wordlist_path.get():
                messagebox.showerror("Error", "Please select a wordlist file.")
                return False
            if not Path(self.wordlist_path.get()).exists():
                messagebox.showerror("Error", "Wordlist file not found.")
                return False

        elif mode == "hybrid":
            if not self.hybrid_wordlist_path.get():
                messagebox.showerror("Error", "Please select a base wordlist file.")
                return False
            if not Path(self.hybrid_wordlist_path.get()).exists():
                messagebox.showerror("Error", "Base wordlist file not found.")
                return False

        return True

    def _get_hashes_to_crack(self):
        """Get list of hashes to crack from input."""
        if self.input_method.get() == "text":
            hash_text = self.hash_text.get('1.0', tk.END).strip()
            if not hash_text:
                return []

            # Extract hashes from text (handle various formats)
            hashes = []
            for line in hash_text.split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue

                # Handle different formats
                if ':' in line:
                    # Format: user:hash or hash:password or hash_type:hash:password
                    parts = line.split(':')
                    if len(parts) >= 2:
                        # Try to identify which part is the hash
                        for part in parts:
                            if self.hash_identifier.identify_hash(part):
                                hashes.append(part)
                                break
                else:
                    # Plain hash
                    if self.hash_identifier.identify_hash(line):
                        hashes.append(line)

            return hashes

        elif self.input_method.get() == "file":
            file_path = self.file_path.get()
            if not file_path or not Path(file_path).exists():
                return []

            # Extract hashes from file
            extracted = self.hash_extractor.extract_from_file(file_path)
            hashes = []
            for hash_list in extracted.values():
                hashes.extend(hash_list)

            return hashes

        return []

    def _get_password_generator(self):
        """Get password generator based on selected mode."""
        mode = self.crack_mode.get()

        try:
            if mode == "wordlist":
                return WordlistGenerator(
                    self.wordlist_path.get(),
                    apply_rules=self.apply_rules.get()
                )

            elif mode == "brute_force":
                # Define character sets
                charsets = {
                    "lowercase": "abcdefghijklmnopqrstuvwxyz",
                    "uppercase": "ABCDEFGHIJKLMNOPQRSTUVWXYZ",
                    "mixed_case": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ",
                    "alphanumeric": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789",
                    "all_printable": "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+-=[]{}|;:,.<>?"
                }

                charset = charsets.get(self.charset_type.get(), charsets["lowercase"])

                return BruteForceGenerator(
                    charset=charset,
                    min_length=self.min_length.get(),
                    max_length=self.max_length.get()
                )

            elif mode == "hybrid":
                return HybridGenerator(
                    self.hybrid_wordlist_path.get(),
                    append_digits=self.append_digits.get(),
                    append_special=self.append_special.get()
                )

        except Exception as e:
            messagebox.showerror("Error", f"Failed to create password generator:\n{e}")
            return None

        return None

    # Placeholder methods for menu actions
    def _save_session(self):
        """Save current session."""
        messagebox.showinfo("Info", "Session save functionality will be implemented in the next update.")

    def _load_session(self):
        """Load saved session."""
        messagebox.showinfo("Info", "Session load functionality will be implemented in the next update.")

    def _export_results(self):
        """Export cracking results."""
        messagebox.showinfo("Info", "Results export functionality will be implemented in the next update.")

    def _clear_all(self):
        """Clear all inputs and results."""
        self.hash_text.delete('1.0', tk.END)
        self.file_path.set("")
        self.wordlist_path.set("")
        self.hybrid_wordlist_path.set("")

        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        self.logger.info("All inputs and results cleared")

    def _show_hash_identifier(self):
        """Show hash identifier tool."""
        messagebox.showinfo("Info", "Hash identifier tool will be implemented in the next update.")

    def _show_password_strength(self):
        """Show password strength test tool."""
        messagebox.showinfo("Info", "Password strength test will be implemented in the next update.")

    def _show_hash_extractor(self):
        """Show hash extractor tool."""
        messagebox.showinfo("Info", "Hash extractor tool will be implemented in the next update.")

    def _show_system_info(self):
        """Show system information."""
        info = get_system_info()
        info_text = "System Information:\n\n"
        for key, value in info.items():
            if key == 'gpu_devices' and isinstance(value, list):
                info_text += f"{key}: {len(value)} devices\n"
                for i, gpu in enumerate(value):
                    info_text += f"  GPU {i}: {gpu.get('name', 'Unknown')}\n"
            else:
                info_text += f"{key}: {value}\n"

        messagebox.showinfo("System Information", info_text)

    def _show_settings(self):
        """Show application settings."""
        messagebox.showinfo("Info", "Settings dialog will be implemented in the next update.")

    def _show_performance_settings(self):
        """Show performance settings."""
        messagebox.showinfo("Info", "Performance settings will be implemented in the next update.")

    def _show_help(self):
        """Show help documentation."""
        messagebox.showinfo("Info", "Help documentation will be implemented in the next update.")

    def _show_ethics(self):
        """Show ethical use guidelines."""
        ethics_text = """
ETHICAL USE GUIDELINES

SimpleCrack is designed for legitimate security testing purposes only.

LEGAL REQUIREMENTS:
• Only use on systems you own or have explicit written permission to test
• Comply with all applicable local, state, and federal laws
• Obtain proper authorization before testing any system

ETHICAL PRINCIPLES:
• Use for defensive security purposes only
• Do not use for unauthorized access or malicious purposes
• Respect privacy and confidentiality
• Report vulnerabilities responsibly

BEST PRACTICES:
• Document all testing activities
• Limit scope to authorized systems only
• Use appropriate safeguards to protect sensitive data
• Follow responsible disclosure practices

Remember: Unauthorized access to computer systems is illegal and unethical.
The developers are not responsible for misuse of this software.
"""
        messagebox.showinfo("Ethical Use Guidelines", ethics_text)

    def _show_about(self):
        """Show about dialog."""
        about_text = """
SimpleCrack v1.0.0

A modern, cross-platform password cracking tool with a graphical user interface
inspired by John the Ripper.

Features:
• Multiple hash format support
• Various cracking modes (wordlist, brute-force, hybrid)
• Multi-threaded processing
• Real-time progress monitoring
• Session management
• Cross-platform compatibility

Developed with Python and Tkinter for educational and legitimate security testing purposes.

For more information, visit: https://github.com/yourusername/SimpleCrack
"""
        messagebox.showinfo("About SimpleCrack", about_text)

    def _show_results_menu(self, event):
        """Show context menu for results tree."""
        try:
            self.results_menu.tk_popup(event.x_root, event.y_root)
        finally:
            self.results_menu.grab_release()

    def _copy_hash(self):
        """Copy selected hash to clipboard."""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            hash_value = item['values'][0]
            self.root.clipboard_clear()
            self.root.clipboard_append(hash_value)

    def _copy_password(self):
        """Copy selected password to clipboard."""
        selection = self.results_tree.selection()
        if selection:
            item = self.results_tree.item(selection[0])
            password = item['values'][1]
            if password:
                self.root.clipboard_clear()
                self.root.clipboard_append(password)

    def _export_selected(self):
        """Export selected results."""
        messagebox.showinfo("Info", "Export selected functionality will be implemented in the next update.")

    def _on_closing(self):
        """Handle application closing."""
        if self.is_cracking:
            if messagebox.askokcancel("Quit", "Cracking is in progress. Do you want to stop and quit?"):
                self.cracking_engine.stop()
                self.root.destroy()
        else:
            self.root.destroy()

    def run(self):
        """Start the GUI main loop."""
        self.root.mainloop()
