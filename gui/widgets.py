"""
SimpleCrack Custom GUI Widgets

This module contains custom GUI widgets and components for SimpleCrack.
"""

import tkinter as tk
from tkinter import ttk
from typing import Dict, List, Optional, Callable, Any


class ProgressFrame(ttk.Frame):
    """Custom progress display frame with detailed statistics."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.progress_var = tk.DoubleVar()
        self.create_widgets()
    
    def create_widgets(self):
        """Create progress widgets."""
        # Progress bar
        self.progress_bar = ttk.Progressbar(
            self, 
            variable=self.progress_var, 
            mode='determinate',
            length=400
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))
        
        # Statistics frame
        stats_frame = ttk.Frame(self)
        stats_frame.pack(fill=tk.X)
        
        # Left column
        left_stats = ttk.Frame(stats_frame)
        left_stats.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        self.attempts_label = ttk.Label(left_stats, text="Attempts: 0")
        self.attempts_label.pack(anchor=tk.W)
        
        self.speed_label = ttk.Label(left_stats, text="Speed: 0 H/s")
        self.speed_label.pack(anchor=tk.W)
        
        self.cracked_label = ttk.Label(left_stats, text="Cracked: 0/0")
        self.cracked_label.pack(anchor=tk.W)
        
        # Right column
        right_stats = ttk.Frame(stats_frame)
        right_stats.pack(side=tk.RIGHT, fill=tk.X, expand=True)
        
        self.elapsed_label = ttk.Label(right_stats, text="Elapsed: 00:00:00")
        self.elapsed_label.pack(anchor=tk.E)
        
        self.eta_label = ttk.Label(right_stats, text="ETA: Unknown")
        self.eta_label.pack(anchor=tk.E)
        
        self.efficiency_label = ttk.Label(right_stats, text="Efficiency: 0%")
        self.efficiency_label.pack(anchor=tk.E)
    
    def update_progress(self, progress_data: Dict[str, Any]):
        """Update progress display with new data."""
        attempts = progress_data.get('total_attempts', 0)
        speed = progress_data.get('speed', 0)
        elapsed = progress_data.get('elapsed_time', 0)
        cracked = progress_data.get('cracked_count', 0)
        total = progress_data.get('total_hashes', 0)
        
        # Update labels
        self.attempts_label.config(text=f"Attempts: {attempts:,}")
        self.speed_label.config(text=f"Speed: {self._format_hash_rate(speed)}")
        self.cracked_label.config(text=f"Cracked: {cracked}/{total}")
        self.elapsed_label.config(text=f"Elapsed: {self._format_time(elapsed)}")
        
        # Update progress bar
        if total > 0:
            progress = (cracked / total) * 100
            self.progress_var.set(progress)
            
            efficiency = (cracked / total) * 100
            self.efficiency_label.config(text=f"Efficiency: {efficiency:.1f}%")
    
    def _format_hash_rate(self, rate: float) -> str:
        """Format hash rate for display."""
        if rate < 1000:
            return f"{rate:.1f} H/s"
        elif rate < 1000000:
            return f"{rate/1000:.1f} KH/s"
        elif rate < 1000000000:
            return f"{rate/1000000:.1f} MH/s"
        else:
            return f"{rate/1000000000:.1f} GH/s"
    
    def _format_time(self, seconds: float) -> str:
        """Format time duration for display."""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"


class ResultsFrame(ttk.Frame):
    """Custom results display frame with filtering and sorting."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.results_data = []
        self.filtered_data = []
        self.sort_column = None
        self.sort_reverse = False
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create results widgets."""
        # Filter frame
        filter_frame = ttk.Frame(self)
        filter_frame.pack(fill=tk.X, pady=(0, 5))
        
        ttk.Label(filter_frame, text="Filter:").pack(side=tk.LEFT)
        
        self.filter_var = tk.StringVar()
        self.filter_var.trace('w', self._on_filter_changed)
        filter_entry = ttk.Entry(filter_frame, textvariable=self.filter_var)
        filter_entry.pack(side=tk.LEFT, padx=(5, 10), fill=tk.X, expand=True)
        
        self.show_cracked_only = tk.BooleanVar()
        self.show_cracked_only.trace('w', self._on_filter_changed)
        ttk.Checkbutton(filter_frame, text="Show cracked only", 
                       variable=self.show_cracked_only).pack(side=tk.LEFT)
        
        # Results tree
        columns = ('Hash', 'Password', 'Time', 'Status')
        self.tree = ttk.Treeview(self, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.tree.heading('Hash', text='Hash (truncated)', 
                         command=lambda: self._sort_by_column('Hash'))
        self.tree.heading('Password', text='Password', 
                         command=lambda: self._sort_by_column('Password'))
        self.tree.heading('Time', text='Time Taken', 
                         command=lambda: self._sort_by_column('Time'))
        self.tree.heading('Status', text='Status', 
                         command=lambda: self._sort_by_column('Status'))
        
        self.tree.column('Hash', width=200)
        self.tree.column('Password', width=150)
        self.tree.column('Time', width=100)
        self.tree.column('Status', width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(self, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(self, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack widgets
        self.tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def add_result(self, hash_value: str, password: str = "", time_taken: str = "", status: str = "Pending"):
        """Add a result to the display."""
        result = {
            'hash': hash_value,
            'password': password,
            'time': time_taken,
            'status': status
        }
        self.results_data.append(result)
        self._update_display()
    
    def update_result(self, hash_value: str, password: str = "", time_taken: str = "", status: str = ""):
        """Update an existing result."""
        for result in self.results_data:
            if result['hash'] == hash_value:
                if password:
                    result['password'] = password
                if time_taken:
                    result['time'] = time_taken
                if status:
                    result['status'] = status
                break
        self._update_display()
    
    def clear_results(self):
        """Clear all results."""
        self.results_data.clear()
        self._update_display()
    
    def _on_filter_changed(self, *args):
        """Handle filter changes."""
        self._update_display()
    
    def _sort_by_column(self, column: str):
        """Sort results by column."""
        if self.sort_column == column:
            self.sort_reverse = not self.sort_reverse
        else:
            self.sort_column = column
            self.sort_reverse = False
        
        self._update_display()
    
    def _update_display(self):
        """Update the tree display with filtered and sorted data."""
        # Apply filters
        filter_text = self.filter_var.get().lower()
        show_cracked_only = self.show_cracked_only.get()
        
        self.filtered_data = []
        for result in self.results_data:
            # Apply text filter
            if filter_text and filter_text not in result['hash'].lower() and filter_text not in result['password'].lower():
                continue
            
            # Apply cracked filter
            if show_cracked_only and result['status'] != 'Cracked':
                continue
            
            self.filtered_data.append(result)
        
        # Apply sorting
        if self.sort_column:
            column_map = {
                'Hash': 'hash',
                'Password': 'password',
                'Time': 'time',
                'Status': 'status'
            }
            sort_key = column_map.get(self.sort_column, 'hash')
            self.filtered_data.sort(key=lambda x: x[sort_key], reverse=self.sort_reverse)
        
        # Update tree
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        for result in self.filtered_data:
            hash_display = result['hash'][:32] + "..." if len(result['hash']) > 32 else result['hash']
            self.tree.insert('', tk.END, values=(
                hash_display,
                result['password'],
                result['time'],
                result['status']
            ))


class ConfigFrame(ttk.Frame):
    """Custom configuration frame with dynamic options."""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, **kwargs)
        
        self.config_vars = {}
        self.config_widgets = {}
        self.change_callback = None
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create configuration widgets."""
        # This will be populated dynamically based on configuration needs
        pass
    
    def set_change_callback(self, callback: Callable):
        """Set callback for configuration changes."""
        self.change_callback = callback
    
    def add_option(self, name: str, option_type: str, **kwargs):
        """Add a configuration option."""
        frame = ttk.Frame(self)
        frame.pack(fill=tk.X, pady=2)
        
        label_text = kwargs.get('label', name.replace('_', ' ').title())
        ttk.Label(frame, text=f"{label_text}:").pack(side=tk.LEFT)
        
        if option_type == 'string':
            var = tk.StringVar(value=kwargs.get('default', ''))
            widget = ttk.Entry(frame, textvariable=var)
            
        elif option_type == 'integer':
            var = tk.IntVar(value=kwargs.get('default', 0))
            widget = ttk.Spinbox(frame, textvariable=var, 
                               from_=kwargs.get('min', 0), 
                               to=kwargs.get('max', 100))
            
        elif option_type == 'boolean':
            var = tk.BooleanVar(value=kwargs.get('default', False))
            widget = ttk.Checkbutton(frame, variable=var)
            
        elif option_type == 'choice':
            var = tk.StringVar(value=kwargs.get('default', ''))
            widget = ttk.Combobox(frame, textvariable=var, 
                                values=kwargs.get('choices', []), 
                                state='readonly')
            
        else:
            raise ValueError(f"Unknown option type: {option_type}")
        
        widget.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        
        # Set up change tracking
        if hasattr(var, 'trace'):
            var.trace('w', lambda *args, n=name: self._on_option_changed(n))
        
        self.config_vars[name] = var
        self.config_widgets[name] = widget
    
    def get_value(self, name: str):
        """Get the value of a configuration option."""
        var = self.config_vars.get(name)
        return var.get() if var else None
    
    def set_value(self, name: str, value):
        """Set the value of a configuration option."""
        var = self.config_vars.get(name)
        if var:
            var.set(value)
    
    def get_all_values(self) -> Dict[str, Any]:
        """Get all configuration values."""
        return {name: var.get() for name, var in self.config_vars.items()}
    
    def _on_option_changed(self, option_name: str):
        """Handle option change."""
        if self.change_callback:
            self.change_callback(option_name, self.get_value(option_name))
