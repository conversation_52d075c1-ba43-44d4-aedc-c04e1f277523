"""
SimpleCrack Dialog Windows

This module contains various dialog windows for SimpleCrack.
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
from typing import Dict, List, Optional, Any


class AboutDialog:
    """About dialog for SimpleCrack."""
    
    def __init__(self, parent):
        self.parent = parent
        self.dialog = None
    
    def show(self):
        """Show the about dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("About SimpleCrack")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self._create_content()
    
    def _create_content(self):
        """Create dialog content."""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="SimpleCrack", 
                               font=('Arial', 16, 'bold'))
        title_label.pack(pady=(0, 10))
        
        # Version
        version_label = ttk.Label(main_frame, text="Version 1.0.0")
        version_label.pack()
        
        # Description
        desc_text = """
A modern, cross-platform password cracking tool with a graphical user interface
inspired by John the Ripper.

Features:
• Multiple hash format support (MD5, SHA-1, SHA-256, SHA-512, bcrypt, NTLM, MySQL, ZIP)
• Various cracking modes (wordlist, brute-force, hybrid)
• Multi-threaded processing for optimal performance
• Real-time progress monitoring and statistics
• Session management and result export
• Cross-platform compatibility (Windows, Linux, macOS)
• Intuitive GUI with advanced configuration options

Developed with Python and Tkinter for educational and legitimate security testing purposes.
"""
        
        desc_label = ttk.Label(main_frame, text=desc_text, justify=tk.LEFT)
        desc_label.pack(pady=20, fill=tk.X)
        
        # Legal notice
        legal_frame = ttk.LabelFrame(main_frame, text="Legal Notice", padding=10)
        legal_frame.pack(fill=tk.X, pady=(0, 20))
        
        legal_text = """This tool is designed for legitimate security testing purposes only.
Users must comply with all applicable laws and obtain proper authorization
before testing any systems. The developers are not responsible for misuse."""
        
        legal_label = ttk.Label(legal_frame, text=legal_text, justify=tk.LEFT, 
                               foreground='red', font=('Arial', 9))
        legal_label.pack()
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        ttk.Button(button_frame, text="GitHub Repository", 
                  command=self._open_github).pack(side=tk.LEFT)
        ttk.Button(button_frame, text="Close", 
                  command=self.dialog.destroy).pack(side=tk.RIGHT)
    
    def _open_github(self):
        """Open GitHub repository."""
        import webbrowser
        webbrowser.open("https://github.com/yourusername/SimpleCrack")


class HelpDialog:
    """Help dialog for SimpleCrack."""
    
    def __init__(self, parent):
        self.parent = parent
        self.dialog = None
    
    def show(self):
        """Show the help dialog."""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("SimpleCrack Help")
        self.dialog.geometry("700x500")
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self._create_content()
    
    def _create_content(self):
        """Create help content."""
        main_frame = ttk.Frame(self.dialog, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for different help sections
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Getting Started tab
        getting_started_frame = ttk.Frame(notebook)
        notebook.add(getting_started_frame, text="Getting Started")
        self._create_getting_started_content(getting_started_frame)
        
        # Hash Types tab
        hash_types_frame = ttk.Frame(notebook)
        notebook.add(hash_types_frame, text="Hash Types")
        self._create_hash_types_content(hash_types_frame)
        
        # Cracking Modes tab
        cracking_modes_frame = ttk.Frame(notebook)
        notebook.add(cracking_modes_frame, text="Cracking Modes")
        self._create_cracking_modes_content(cracking_modes_frame)
        
        # Tips & Tricks tab
        tips_frame = ttk.Frame(notebook)
        notebook.add(tips_frame, text="Tips & Tricks")
        self._create_tips_content(tips_frame)
        
        # Close button
        ttk.Button(main_frame, text="Close", 
                  command=self.dialog.destroy).pack(pady=(10, 0))
    
    def _create_getting_started_content(self, parent):
        """Create getting started content."""
        text_widget = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=20)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        content = """
GETTING STARTED WITH SIMPLECRACK

1. INPUT HASHES
   • Paste hashes directly into the text area, or
   • Use "Browse" to load a hash file
   • Supported formats: plain text, shadow files, pwdump files

2. SELECT HASH TYPE
   • Use "Auto-Detect Hash Types" for automatic detection
   • Manually select from the dropdown if needed
   • Supported types: MD5, SHA-1, SHA-256, SHA-512, bcrypt, NTLM, MySQL, ZIP

3. CHOOSE CRACKING MODE
   • Wordlist: Use a dictionary file with optional rules
   • Brute-force: Generate passwords systematically
   • Hybrid: Combine wordlist with brute-force techniques

4. CONFIGURE SETTINGS
   • Set number of threads for optimal performance
   • Enable GPU acceleration if available
   • Adjust mode-specific parameters

5. START CRACKING
   • Click "Start Cracking" to begin
   • Monitor progress in real-time
   • Use "Pause" or "Stop" to control the process

6. VIEW RESULTS
   • Cracked passwords appear in the results table
   • Use filters to find specific results
   • Export results for further analysis

IMPORTANT: Always ensure you have proper authorization before testing any systems!
"""
        
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def _create_hash_types_content(self, parent):
        """Create hash types content."""
        text_widget = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=20)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        content = """
SUPPORTED HASH TYPES

MD5 (Message Digest 5)
• Length: 32 hexadecimal characters
• Security: Cryptographically broken, very fast to crack
• Common uses: Legacy systems, file checksums
• Example: 5d41402abc4b2a76b9719d911017c592

SHA-1 (Secure Hash Algorithm 1)
• Length: 40 hexadecimal characters
• Security: Cryptographically weak, fast to crack
• Common uses: Legacy systems, Git commits
• Example: aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d

SHA-256 (Secure Hash Algorithm 256)
• Length: 64 hexadecimal characters
• Security: Cryptographically secure, slower to crack
• Common uses: Modern systems, Bitcoin
• Example: 2cf24dba4f21d4288094c8b0f5b6ce155d1bd83f56b2a3c8329d2e5b3dc8b5d4

SHA-512 (Secure Hash Algorithm 512)
• Length: 128 hexadecimal characters
• Security: Cryptographically secure, slow to crack
• Common uses: High-security applications
• Example: 9b71d224bd62f3785d96d46ad3ea3d73319bfbc2890caadae2dff72519673ca7...

NTLM (NT LAN Manager)
• Length: 32 hexadecimal characters
• Security: Weak, very fast to crack
• Common uses: Windows password hashes
• Example: 8846f7eaee8fb117ad06bdd830b7586c

bcrypt
• Length: 60 characters with specific format
• Security: Very secure, designed to be slow
• Common uses: Modern web applications
• Example: $2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uIoS

MySQL
• Length: 41 characters starting with *
• Security: Weak, fast to crack
• Common uses: MySQL database passwords
• Example: *6BB4837EB74329105EE4568DDA7DC67ED2CA2AD9

ZIP
• Variable length with specific format
• Security: Depends on encryption method
• Common uses: Password-protected ZIP files
• Example: $zip$*0*1*0*...
"""
        
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def _create_cracking_modes_content(self, parent):
        """Create cracking modes content."""
        text_widget = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=20)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        content = """
CRACKING MODES

WORDLIST MODE
• Uses a dictionary file containing potential passwords
• Most effective for common passwords
• Options:
  - Apply rules: Adds variations like case changes, numbers
  - Custom wordlists: Use your own dictionary files
• Best for: Common passwords, targeted attacks
• Speed: Fast (depends on wordlist size)

BRUTE-FORCE MODE
• Generates passwords systematically
• Tests all possible combinations within specified parameters
• Options:
  - Character set: lowercase, uppercase, mixed, alphanumeric, all printable
  - Length range: minimum and maximum password length
• Best for: Short passwords, when wordlists fail
• Speed: Slow (exponentially increases with length)

HYBRID MODE
• Combines wordlist with brute-force techniques
• Takes base words and adds variations
• Options:
  - Append digits: Add numbers to the end of words
  - Prepend digits: Add numbers to the beginning
  - Append special characters: Add symbols
• Best for: Passwords based on common words with modifications
• Speed: Medium (faster than pure brute-force)

PERFORMANCE TIPS:
• Use multiple threads to utilize all CPU cores
• Enable GPU acceleration for supported hash types
• Start with wordlist mode for best results
• Use hybrid mode for passwords with common patterns
• Reserve brute-force for short passwords only

WORDLIST RECOMMENDATIONS:
• rockyou.txt: Popular leaked passwords
• SecLists: Comprehensive password lists
• Custom lists: Industry-specific or targeted wordlists
• Smaller lists: For faster initial attempts
"""
        
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)
    
    def _create_tips_content(self, parent):
        """Create tips and tricks content."""
        text_widget = scrolledtext.ScrolledText(parent, wrap=tk.WORD, height=20)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        content = """
TIPS & TRICKS

OPTIMIZATION:
• Set thread count to match your CPU cores
• Monitor CPU usage and adjust threads accordingly
• Use GPU acceleration for bcrypt and other slow hashes
• Close unnecessary applications to free up resources

WORDLIST STRATEGIES:
• Start with small, high-quality wordlists
• Use targeted wordlists for specific environments
• Combine multiple wordlists for better coverage
• Create custom wordlists based on target information

HASH IDENTIFICATION:
• Use the auto-detect feature for unknown hashes
• Check hash length and format for manual identification
• Be aware that MD5 and NTLM have the same length
• Context clues help distinguish similar formats

SESSION MANAGEMENT:
• Save sessions for long-running attacks
• Resume interrupted sessions to avoid losing progress
• Export results regularly for backup
• Document your testing activities

TROUBLESHOOTING:
• Check file permissions for wordlist and hash files
• Verify hash format matches selected type
• Ensure sufficient disk space for large operations
• Monitor memory usage with large wordlists

ETHICAL CONSIDERATIONS:
• Always obtain proper authorization
• Document all testing activities
• Use appropriate safeguards for sensitive data
• Follow responsible disclosure practices
• Respect privacy and confidentiality

PERFORMANCE EXPECTATIONS:
• MD5/SHA-1: Very fast (millions of hashes per second)
• SHA-256/SHA-512: Moderate speed
• bcrypt: Intentionally slow (hundreds per second)
• NTLM: Very fast on GPU
• ZIP: Varies by encryption method

COMMON MISTAKES:
• Using wrong hash type
• Insufficient wordlist coverage
• Not using rules with wordlists
• Setting unrealistic brute-force parameters
• Ignoring legal and ethical requirements
"""
        
        text_widget.insert(tk.END, content)
        text_widget.config(state=tk.DISABLED)


class SettingsDialog:
    """Settings dialog for SimpleCrack."""
    
    def __init__(self, parent):
        self.parent = parent
        self.dialog = None
        self.settings = {}
    
    def show(self, current_settings: Dict[str, Any] = None):
        """Show the settings dialog."""
        self.settings = current_settings or {}
        
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title("SimpleCrack Settings")
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # Center the dialog
        self.dialog.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 50,
            self.parent.winfo_rooty() + 50
        ))
        
        self._create_content()
    
    def _create_content(self):
        """Create settings content."""
        main_frame = ttk.Frame(self.dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Settings will be implemented in future updates
        ttk.Label(main_frame, text="Settings dialog will be implemented in a future update.").pack()
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
        
        ttk.Button(button_frame, text="OK", command=self._save_settings).pack(side=tk.RIGHT)
        ttk.Button(button_frame, text="Cancel", command=self.dialog.destroy).pack(side=tk.RIGHT, padx=(0, 10))
    
    def _save_settings(self):
        """Save settings and close dialog."""
        # Settings saving will be implemented in future updates
        self.dialog.destroy()
