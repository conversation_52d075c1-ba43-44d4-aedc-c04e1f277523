"""
SimpleCrack Password Cracking Algorithms

This module contains different password cracking algorithms and modes.
"""

import itertools
import logging
import string
import threading
import time
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, List, Optional, Callable, Iterator, Any, Set
from queue import Queue, Empty

from .hash_engine import HashEngine


class CrackingResult:
    """Represents the result of a password cracking attempt."""
    
    def __init__(self, hash_value: str, password: Optional[str] = None, 
                 time_taken: float = 0.0, attempts: int = 0):
        self.hash_value = hash_value
        self.password = password
        self.time_taken = time_taken
        self.attempts = attempts
        self.cracked = password is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            'hash': self.hash_value,
            'password': self.password,
            'cracked': self.cracked,
            'time_taken': self.time_taken,
            'attempts': self.attempts
        }


class PasswordGenerator(ABC):
    """Abstract base class for password generators."""
    
    @abstractmethod
    def generate(self) -> Iterator[str]:
        """Generate passwords."""
        pass
    
    @abstractmethod
    def estimate_total(self) -> Optional[int]:
        """Estimate total number of passwords to generate."""
        pass


class WordlistGenerator(PasswordGenerator):
    """Generate passwords from a wordlist file."""
    
    def __init__(self, wordlist_path: str, apply_rules: bool = False):
        self.wordlist_path = Path(wordlist_path)
        self.apply_rules = apply_rules
        self.logger = logging.getLogger(__name__)
        
        if not self.wordlist_path.exists():
            raise FileNotFoundError(f"Wordlist file not found: {wordlist_path}")
    
    def generate(self) -> Iterator[str]:
        """Generate passwords from wordlist."""
        try:
            with open(self.wordlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    password = line.strip()
                    if password:
                        yield password
                        
                        # Apply rules if enabled
                        if self.apply_rules:
                            yield from self._apply_rules(password)
        except Exception as e:
            self.logger.error(f"Error reading wordlist: {e}")
    
    def _apply_rules(self, password: str) -> Iterator[str]:
        """Apply common password rules/mutations."""
        # Common mutations
        mutations = [
            password.upper(),
            password.lower(),
            password.capitalize(),
            password + '1',
            password + '123',
            password + '!',
            '1' + password,
            password + password[::-1],  # password + reverse
        ]
        
        # Add years
        for year in range(1990, 2030):
            mutations.append(password + str(year))
            mutations.append(str(year) + password)
        
        # Remove duplicates and original
        seen = {password}
        for mutation in mutations:
            if mutation not in seen:
                seen.add(mutation)
                yield mutation
    
    def estimate_total(self) -> Optional[int]:
        """Estimate total passwords in wordlist."""
        try:
            with open(self.wordlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                count = sum(1 for line in f if line.strip())
                
                # If rules are applied, multiply by estimated rule factor
                if self.apply_rules:
                    count *= 15  # Rough estimate of rule multiplier
                
                return count
        except Exception:
            return None


class BruteForceGenerator(PasswordGenerator):
    """Generate passwords using brute force."""
    
    def __init__(self, charset: str = None, min_length: int = 1, max_length: int = 8):
        self.charset = charset or (string.ascii_lowercase + string.digits)
        self.min_length = min_length
        self.max_length = max_length
        self.logger = logging.getLogger(__name__)
    
    def generate(self) -> Iterator[str]:
        """Generate passwords using brute force."""
        for length in range(self.min_length, self.max_length + 1):
            for combination in itertools.product(self.charset, repeat=length):
                yield ''.join(combination)
    
    def estimate_total(self) -> Optional[int]:
        """Estimate total passwords to generate."""
        total = 0
        charset_len = len(self.charset)
        
        for length in range(self.min_length, self.max_length + 1):
            total += charset_len ** length
        
        return total


class HybridGenerator(PasswordGenerator):
    """Generate passwords using hybrid approach (wordlist + brute force)."""
    
    def __init__(self, wordlist_path: str, append_digits: int = 2, 
                 prepend_digits: int = 0, append_special: bool = True):
        self.wordlist_path = Path(wordlist_path)
        self.append_digits = append_digits
        self.prepend_digits = prepend_digits
        self.append_special = append_special
        self.logger = logging.getLogger(__name__)
        
        if not self.wordlist_path.exists():
            raise FileNotFoundError(f"Wordlist file not found: {wordlist_path}")
    
    def generate(self) -> Iterator[str]:
        """Generate hybrid passwords."""
        try:
            with open(self.wordlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    base_word = line.strip()
                    if not base_word:
                        continue
                    
                    # Base word
                    yield base_word
                    
                    # Append digits
                    if self.append_digits > 0:
                        for combo in itertools.product(string.digits, repeat=self.append_digits):
                            yield base_word + ''.join(combo)
                    
                    # Prepend digits
                    if self.prepend_digits > 0:
                        for combo in itertools.product(string.digits, repeat=self.prepend_digits):
                            yield ''.join(combo) + base_word
                    
                    # Append special characters
                    if self.append_special:
                        for char in '!@#$%^&*':
                            yield base_word + char
                            
                            # Combine with digits
                            if self.append_digits > 0:
                                for combo in itertools.product(string.digits, repeat=min(2, self.append_digits)):
                                    yield base_word + char + ''.join(combo)
        
        except Exception as e:
            self.logger.error(f"Error in hybrid generation: {e}")
    
    def estimate_total(self) -> Optional[int]:
        """Estimate total passwords to generate."""
        try:
            with open(self.wordlist_path, 'r', encoding='utf-8', errors='ignore') as f:
                word_count = sum(1 for line in f if line.strip())
            
            # Rough estimation
            multiplier = 1  # Base words
            
            if self.append_digits > 0:
                multiplier += 10 ** self.append_digits
            
            if self.prepend_digits > 0:
                multiplier += 10 ** self.prepend_digits
            
            if self.append_special:
                multiplier += 8  # Number of special chars
                if self.append_digits > 0:
                    multiplier += 8 * (10 ** min(2, self.append_digits))
            
            return word_count * multiplier

        except Exception:
            return None


class CrackingEngine:
    """Main password cracking engine."""

    def __init__(self, hash_engine: HashEngine, num_threads: int = 4):
        self.hash_engine = hash_engine
        self.num_threads = num_threads
        self.logger = logging.getLogger(__name__)

        # Threading control
        self.stop_event = threading.Event()
        self.pause_event = threading.Event()
        self.threads: List[threading.Thread] = []

        # Progress tracking
        self.total_attempts = 0
        self.start_time = 0.0
        self.results: Dict[str, CrackingResult] = {}
        self.progress_callback: Optional[Callable] = None

        # Thread-safe queues
        self.password_queue = Queue(maxsize=1000)
        self.result_queue = Queue()

    def set_progress_callback(self, callback: Callable):
        """Set callback function for progress updates."""
        self.progress_callback = callback

    def crack_hashes(self, hashes: List[str], hash_type: str,
                    generator: PasswordGenerator) -> Dict[str, CrackingResult]:
        """
        Crack multiple hashes using the specified generator.

        Args:
            hashes: List of hash values to crack
            hash_type: Type of hash (md5, sha1, etc.)
            generator: Password generator to use

        Returns:
            Dictionary mapping hash values to results
        """
        self.logger.info(f"Starting crack session: {len(hashes)} hashes, type: {hash_type}")

        # Reset state
        self.stop_event.clear()
        self.pause_event.clear()
        self.total_attempts = 0
        self.start_time = time.time()
        self.results = {h: CrackingResult(h) for h in hashes}

        # Start worker threads
        self._start_worker_threads(hashes, hash_type)

        # Start password generator thread
        generator_thread = threading.Thread(
            target=self._password_generator_worker,
            args=(generator,),
            daemon=True
        )
        generator_thread.start()

        # Monitor progress
        self._monitor_progress()

        # Wait for completion or stop
        generator_thread.join()
        self._stop_worker_threads()

        # Calculate final statistics
        end_time = time.time()
        total_time = end_time - self.start_time

        for result in self.results.values():
            if not result.cracked:
                result.time_taken = total_time
                result.attempts = self.total_attempts

        self.logger.info(f"Crack session completed: {sum(1 for r in self.results.values() if r.cracked)} cracked")

        return self.results

    def _start_worker_threads(self, hashes: List[str], hash_type: str):
        """Start worker threads for password testing."""
        self.threads = []

        for i in range(self.num_threads):
            thread = threading.Thread(
                target=self._worker_thread,
                args=(hashes, hash_type, i),
                daemon=True
            )
            thread.start()
            self.threads.append(thread)

        self.logger.debug(f"Started {len(self.threads)} worker threads")

    def _stop_worker_threads(self):
        """Stop all worker threads."""
        self.stop_event.set()

        # Add sentinel values to wake up threads
        for _ in range(self.num_threads):
            try:
                self.password_queue.put(None, timeout=1)
            except:
                pass

        # Wait for threads to finish
        for thread in self.threads:
            thread.join(timeout=5)

        self.threads = []
        self.logger.debug("All worker threads stopped")

    def _worker_thread(self, hashes: List[str], hash_type: str, thread_id: int):
        """Worker thread for testing passwords against hashes."""
        self.logger.debug(f"Worker thread {thread_id} started")

        while not self.stop_event.is_set():
            try:
                # Wait if paused
                if self.pause_event.is_set():
                    time.sleep(0.1)
                    continue

                # Get password from queue
                password = self.password_queue.get(timeout=1)
                if password is None:  # Sentinel value
                    break

                # Test password against all uncracked hashes
                for hash_value in hashes:
                    if self.results[hash_value].cracked:
                        continue

                    if self.hash_engine.verify_password(password, hash_value, hash_type):
                        # Password found!
                        result = self.results[hash_value]
                        result.password = password
                        result.time_taken = time.time() - self.start_time
                        result.attempts = self.total_attempts

                        self.logger.info(f"Password cracked: {hash_value[:16]}... = {password}")

                        # Check if all hashes are cracked
                        if all(r.cracked for r in self.results.values()):
                            self.logger.info("All hashes cracked, stopping")
                            self.stop_event.set()
                            break

                self.total_attempts += 1

                # Mark task as done
                self.password_queue.task_done()

            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"Error in worker thread {thread_id}: {e}")

        self.logger.debug(f"Worker thread {thread_id} finished")

    def _password_generator_worker(self, generator: PasswordGenerator):
        """Worker thread for generating passwords."""
        self.logger.debug("Password generator thread started")

        try:
            for password in generator.generate():
                if self.stop_event.is_set():
                    break

                # Wait if paused
                while self.pause_event.is_set() and not self.stop_event.is_set():
                    time.sleep(0.1)

                # Add password to queue
                self.password_queue.put(password, timeout=1)

        except Exception as e:
            self.logger.error(f"Error in password generator: {e}")
        finally:
            # Signal end of passwords
            for _ in range(self.num_threads):
                try:
                    self.password_queue.put(None, timeout=1)
                except:
                    pass

            self.logger.debug("Password generator thread finished")

    def _monitor_progress(self):
        """Monitor and report progress."""
        last_update = time.time()
        last_attempts = 0

        while not self.stop_event.is_set():
            time.sleep(1)  # Update every second

            current_time = time.time()
            if current_time - last_update >= 1.0:  # Update every second
                # Calculate speed
                attempts_delta = self.total_attempts - last_attempts
                time_delta = current_time - last_update
                speed = attempts_delta / time_delta if time_delta > 0 else 0

                # Call progress callback if set
                if self.progress_callback:
                    progress_data = {
                        'total_attempts': self.total_attempts,
                        'speed': speed,
                        'elapsed_time': current_time - self.start_time,
                        'cracked_count': sum(1 for r in self.results.values() if r.cracked),
                        'total_hashes': len(self.results)
                    }
                    self.progress_callback(progress_data)

                last_update = current_time
                last_attempts = self.total_attempts

    def pause(self):
        """Pause the cracking process."""
        self.pause_event.set()
        self.logger.info("Cracking paused")

    def resume(self):
        """Resume the cracking process."""
        self.pause_event.clear()
        self.logger.info("Cracking resumed")

    def stop(self):
        """Stop the cracking process."""
        self.stop_event.set()
        self.logger.info("Cracking stopped")

    def is_running(self) -> bool:
        """Check if cracking is currently running."""
        return any(thread.is_alive() for thread in self.threads)

    def is_paused(self) -> bool:
        """Check if cracking is currently paused."""
        return self.pause_event.is_set()
