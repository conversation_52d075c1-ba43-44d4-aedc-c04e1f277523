"""
SimpleCrack Hash Processing Engine

This module contains the core hash processing functionality supporting
multiple hash formats and algorithms.
"""

import hashlib
import logging
from typing import Dict, List, Optional, Callable, Any
from abc import ABC, abstractmethod


class HashProcessor(ABC):
    """Abstract base class for hash processors."""
    
    @abstractmethod
    def hash_password(self, password: str) -> str:
        """Hash a password using this processor's algorithm."""
        pass
    
    @abstractmethod
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against a hash."""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of this hash processor."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get a description of this hash processor."""
        pass


class MD5Processor(HashProcessor):
    """MD5 hash processor."""
    
    def hash_password(self, password: str) -> str:
        """Hash a password using MD5."""
        return hashlib.md5(password.encode('utf-8')).hexdigest()
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against an MD5 hash."""
        return self.hash_password(password).lower() == hash_value.lower()
    
    def get_name(self) -> str:
        return "MD5"
    
    def get_description(self) -> str:
        return "MD5 (Message Digest 5) - 128-bit hash function"


class SHA1Processor(HashProcessor):
    """SHA-1 hash processor."""
    
    def hash_password(self, password: str) -> str:
        """Hash a password using SHA-1."""
        return hashlib.sha1(password.encode('utf-8')).hexdigest()
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against a SHA-1 hash."""
        return self.hash_password(password).lower() == hash_value.lower()
    
    def get_name(self) -> str:
        return "SHA-1"
    
    def get_description(self) -> str:
        return "SHA-1 (Secure Hash Algorithm 1) - 160-bit hash function"


class SHA256Processor(HashProcessor):
    """SHA-256 hash processor."""
    
    def hash_password(self, password: str) -> str:
        """Hash a password using SHA-256."""
        return hashlib.sha256(password.encode('utf-8')).hexdigest()
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against a SHA-256 hash."""
        return self.hash_password(password).lower() == hash_value.lower()
    
    def get_name(self) -> str:
        return "SHA-256"
    
    def get_description(self) -> str:
        return "SHA-256 (Secure Hash Algorithm 256) - 256-bit hash function"


class SHA512Processor(HashProcessor):
    """SHA-512 hash processor."""
    
    def hash_password(self, password: str) -> str:
        """Hash a password using SHA-512."""
        return hashlib.sha512(password.encode('utf-8')).hexdigest()
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against a SHA-512 hash."""
        return self.hash_password(password).lower() == hash_value.lower()
    
    def get_name(self) -> str:
        return "SHA-512"
    
    def get_description(self) -> str:
        return "SHA-512 (Secure Hash Algorithm 512) - 512-bit hash function"


class NTLMProcessor(HashProcessor):
    """NTLM hash processor."""
    
    def hash_password(self, password: str) -> str:
        """Hash a password using NTLM."""
        # NTLM uses MD4 hash of UTF-16LE encoded password
        try:
            from Crypto.Hash import MD4
            return MD4.new(password.encode('utf-16le')).hexdigest()
        except ImportError:
            # Fallback using hashlib if available
            try:
                return hashlib.new('md4', password.encode('utf-16le')).hexdigest()
            except ValueError:
                raise ImportError("MD4 not available. Install pycryptodome for NTLM support.")
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against an NTLM hash."""
        return self.hash_password(password).lower() == hash_value.lower()
    
    def get_name(self) -> str:
        return "NTLM"
    
    def get_description(self) -> str:
        return "NTLM (NT LAN Manager) - Windows password hash"


class BcryptProcessor(HashProcessor):
    """Bcrypt hash processor."""
    
    def __init__(self):
        try:
            import bcrypt
            self.bcrypt = bcrypt
        except ImportError:
            raise ImportError("bcrypt library required for bcrypt support")
    
    def hash_password(self, password: str, rounds: int = 12) -> str:
        """Hash a password using bcrypt."""
        salt = self.bcrypt.gensalt(rounds=rounds)
        return self.bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against a bcrypt hash."""
        try:
            return self.bcrypt.checkpw(password.encode('utf-8'), hash_value.encode('utf-8'))
        except Exception:
            return False
    
    def get_name(self) -> str:
        return "bcrypt"
    
    def get_description(self) -> str:
        return "bcrypt - Adaptive hash function based on Blowfish cipher"


class MySQLProcessor(HashProcessor):
    """MySQL hash processor (MySQL 4.1+ format)."""
    
    def hash_password(self, password: str) -> str:
        """Hash a password using MySQL format."""
        # MySQL uses SHA1(SHA1(password))
        stage1 = hashlib.sha1(password.encode('utf-8')).digest()
        stage2 = hashlib.sha1(stage1).hexdigest()
        return '*' + stage2.upper()
    
    def verify_password(self, password: str, hash_value: str) -> bool:
        """Verify a password against a MySQL hash."""
        return self.hash_password(password).upper() == hash_value.upper()
    
    def get_name(self) -> str:
        return "MySQL"
    
    def get_description(self) -> str:
        return "MySQL 4.1+ password hash format"


class HashEngine:
    """Main hash processing engine that manages different hash processors."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processors: Dict[str, HashProcessor] = {}
        self._register_default_processors()
    
    def _register_default_processors(self):
        """Register default hash processors."""
        processors = [
            MD5Processor(),
            SHA1Processor(),
            SHA256Processor(),
            SHA512Processor(),
            NTLMProcessor(),
            MySQLProcessor(),
        ]
        
        # Add bcrypt if available
        try:
            processors.append(BcryptProcessor())
        except ImportError:
            self.logger.warning("bcrypt not available, skipping bcrypt processor")
        
        for processor in processors:
            self.register_processor(processor)
    
    def register_processor(self, processor: HashProcessor):
        """Register a hash processor."""
        name = processor.get_name().lower()
        self.processors[name] = processor
        self.logger.debug(f"Registered hash processor: {processor.get_name()}")
    
    def get_processor(self, hash_type: str) -> Optional[HashProcessor]:
        """Get a hash processor by type."""
        return self.processors.get(hash_type.lower())
    
    def get_available_processors(self) -> List[str]:
        """Get list of available hash processor names."""
        return list(self.processors.keys())
    
    def get_processor_info(self, hash_type: str) -> Optional[Dict[str, str]]:
        """Get information about a hash processor."""
        processor = self.get_processor(hash_type)
        if processor:
            return {
                'name': processor.get_name(),
                'description': processor.get_description()
            }
        return None
    
    def hash_password(self, password: str, hash_type: str, **kwargs) -> Optional[str]:
        """Hash a password using the specified hash type."""
        processor = self.get_processor(hash_type)
        if not processor:
            self.logger.error(f"Unknown hash type: {hash_type}")
            return None
        
        try:
            if hash_type.lower() == 'bcrypt' and 'rounds' in kwargs:
                return processor.hash_password(password, rounds=kwargs['rounds'])
            else:
                return processor.hash_password(password)
        except Exception as e:
            self.logger.error(f"Error hashing password with {hash_type}: {e}")
            return None
    
    def verify_password(self, password: str, hash_value: str, hash_type: str) -> bool:
        """Verify a password against a hash."""
        processor = self.get_processor(hash_type)
        if not processor:
            self.logger.error(f"Unknown hash type: {hash_type}")
            return False
        
        try:
            return processor.verify_password(password, hash_value)
        except Exception as e:
            self.logger.error(f"Error verifying password with {hash_type}: {e}")
            return False
