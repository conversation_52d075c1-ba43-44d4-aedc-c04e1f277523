"""
SimpleCrack Core Utilities

This module contains utility functions for logging, dependency checking,
and other common operations.
"""

import os
import sys
import logging
import importlib
from pathlib import Path
from typing import List, Optional


def setup_logging(log_level: str = "INFO") -> None:
    """
    Set up logging configuration for the application.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # Create logs directory if it doesn't exist
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # Configure logging
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        format=log_format,
        handlers=[
            logging.FileHandler(log_dir / "simplecrack.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )
    
    # Set up logger for this module
    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized with level: {log_level}")


def check_dependencies() -> List[str]:
    """
    Check if all required dependencies are available.
    
    Returns:
        List of missing dependencies
    """
    required_modules = [
        'bcrypt',
        'passlib',
        'rarfile',
        'py7zr',
        'requests',
        'Crypto'  # pycryptodome
    ]
    
    optional_modules = [
        'pyopencl'  # For GPU acceleration
    ]
    
    missing = []
    logger = logging.getLogger(__name__)
    
    # Check required modules
    for module in required_modules:
        try:
            importlib.import_module(module)
            logger.debug(f"Required module '{module}' found")
        except ImportError:
            missing.append(module)
            logger.warning(f"Required module '{module}' not found")
    
    # Check optional modules (warn but don't fail)
    for module in optional_modules:
        try:
            importlib.import_module(module)
            logger.debug(f"Optional module '{module}' found")
        except ImportError:
            logger.info(f"Optional module '{module}' not found (GPU acceleration disabled)")
    
    return missing


def validate_hash_format(hash_string: str) -> Optional[str]:
    """
    Attempt to identify the hash format based on length and characteristics.
    
    Args:
        hash_string: The hash string to analyze
        
    Returns:
        Detected hash type or None if unknown
    """
    if not hash_string or not isinstance(hash_string, str):
        return None
    
    # Remove whitespace and convert to lowercase for analysis
    clean_hash = hash_string.strip().lower()
    
    # Common hash format patterns
    hash_patterns = {
        'md5': (32, r'^[a-f0-9]{32}$'),
        'sha1': (40, r'^[a-f0-9]{40}$'),
        'sha256': (64, r'^[a-f0-9]{64}$'),
        'sha512': (128, r'^[a-f0-9]{128}$'),
        'ntlm': (32, r'^[a-f0-9]{32}$'),  # Same as MD5, need context
        'bcrypt': (60, r'^\$2[aby]?\$[0-9]{2}\$[./A-Za-z0-9]{53}$'),
        'mysql': (41, r'^\*[a-f0-9]{40}$'),
    }
    
    import re
    
    # Check bcrypt first (has distinctive format)
    if re.match(hash_patterns['bcrypt'][1], clean_hash):
        return 'bcrypt'
    
    # Check MySQL format
    if re.match(hash_patterns['mysql'][1], clean_hash):
        return 'mysql'
    
    # Check by length for other formats
    length = len(clean_hash)
    if length == 32 and re.match(r'^[a-f0-9]{32}$', clean_hash):
        return 'md5'  # Could also be NTLM, but MD5 is more common
    elif length == 40 and re.match(r'^[a-f0-9]{40}$', clean_hash):
        return 'sha1'
    elif length == 64 and re.match(r'^[a-f0-9]{64}$', clean_hash):
        return 'sha256'
    elif length == 128 and re.match(r'^[a-f0-9]{128}$', clean_hash):
        return 'sha512'
    
    return None


def sanitize_filename(filename: str) -> str:
    """
    Sanitize a filename to be safe for filesystem use.
    
    Args:
        filename: The filename to sanitize
        
    Returns:
        Sanitized filename
    """
    import re
    
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')
    
    # Ensure it's not empty
    if not sanitized:
        sanitized = "unnamed"
    
    # Limit length
    if len(sanitized) > 255:
        sanitized = sanitized[:255]
    
    return sanitized


def format_time_duration(seconds: float) -> str:
    """
    Format a duration in seconds to a human-readable string.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        Formatted duration string
    """
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    elif seconds < 86400:
        hours = seconds / 3600
        return f"{hours:.1f}h"
    else:
        days = seconds / 86400
        return f"{days:.1f}d"


def format_hash_rate(hashes_per_second: float) -> str:
    """
    Format hash rate to a human-readable string.
    
    Args:
        hashes_per_second: Hash rate in hashes per second
        
    Returns:
        Formatted hash rate string
    """
    if hashes_per_second < 1000:
        return f"{hashes_per_second:.1f} H/s"
    elif hashes_per_second < 1000000:
        return f"{hashes_per_second/1000:.1f} KH/s"
    elif hashes_per_second < 1000000000:
        return f"{hashes_per_second/1000000:.1f} MH/s"
    else:
        return f"{hashes_per_second/1000000000:.1f} GH/s"


def ensure_directory_exists(directory_path: str) -> Path:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        directory_path: Path to the directory
        
    Returns:
        Path object for the directory
    """
    path = Path(directory_path)
    path.mkdir(parents=True, exist_ok=True)
    return path


def get_system_info() -> dict:
    """
    Get system information for performance optimization.
    
    Returns:
        Dictionary containing system information
    """
    import platform
    import multiprocessing
    
    info = {
        'platform': platform.system(),
        'platform_version': platform.version(),
        'architecture': platform.architecture()[0],
        'processor': platform.processor(),
        'cpu_count': multiprocessing.cpu_count(),
        'python_version': platform.python_version(),
    }
    
    # Try to get GPU information if PyOpenCL is available
    try:
        import pyopencl as cl  # type: ignore # Optional dependency for GPU acceleration
        platforms = cl.get_platforms()
        gpu_info = []
        for platform in platforms:
            devices = platform.get_devices()
            for device in devices:
                gpu_info.append({
                    'name': device.name,
                    'type': cl.device_type.to_string(device.type),
                    'vendor': device.vendor,
                    'max_compute_units': device.max_compute_units,
                    'global_mem_size': device.global_mem_size
                })
        info['gpu_devices'] = gpu_info
    except ImportError:
        info['gpu_devices'] = []
    
    return info
