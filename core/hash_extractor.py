"""
SimpleCrack Hash Extraction Utilities

This module provides utilities for extracting hashes from various file formats
and automatically identifying hash types.
"""

import logging
import re
import zipfile
import rarfile
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Set
import struct

from .utils import validate_hash_format
from plugins.hash_formats.zip_hash import extract_zip_hashes


class HashExtractor:
    """Main class for extracting hashes from various sources."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Hash patterns for identification
        self.hash_patterns = {
            'md5': re.compile(r'\b[a-fA-F0-9]{32}\b'),
            'sha1': re.compile(r'\b[a-fA-F0-9]{40}\b'),
            'sha256': re.compile(r'\b[a-fA-F0-9]{64}\b'),
            'sha512': re.compile(r'\b[a-fA-F0-9]{128}\b'),
            'ntlm': re.compile(r'\b[a-fA-F0-9]{32}\b'),  # Same as MD5, context dependent
            'bcrypt': re.compile(r'\$2[aby]?\$[0-9]{2}\$[./A-Za-z0-9]{53}'),
            'mysql': re.compile(r'\*[a-fA-F0-9]{40}'),
            'sha1_salt': re.compile(r'\b[a-fA-F0-9]{40}:[a-fA-F0-9]+\b'),
            'md5_salt': re.compile(r'\b[a-fA-F0-9]{32}:[a-fA-F0-9]+\b'),
        }
    
    def extract_from_text(self, text: str) -> Dict[str, List[str]]:
        """
        Extract hashes from plain text.
        
        Args:
            text: Text content to search for hashes
            
        Returns:
            Dictionary mapping hash types to lists of found hashes
        """
        found_hashes = {}
        
        for hash_type, pattern in self.hash_patterns.items():
            matches = pattern.findall(text)
            if matches:
                # Remove duplicates while preserving order
                unique_matches = list(dict.fromkeys(matches))
                found_hashes[hash_type] = unique_matches
                self.logger.debug(f"Found {len(unique_matches)} {hash_type} hashes")
        
        return found_hashes
    
    def extract_from_file(self, file_path: str) -> Dict[str, List[str]]:
        """
        Extract hashes from a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            Dictionary mapping hash types to lists of found hashes
        """
        path = Path(file_path)
        
        if not path.exists():
            self.logger.error(f"File not found: {file_path}")
            return {}
        
        # Handle different file types
        if path.suffix.lower() == '.zip':
            return self._extract_from_zip(file_path)
        elif path.suffix.lower() == '.rar':
            return self._extract_from_rar(file_path)
        else:
            return self._extract_from_text_file(file_path)
    
    def _extract_from_text_file(self, file_path: str) -> Dict[str, List[str]]:
        """Extract hashes from a text file."""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Special handling for common formats
            if '/etc/shadow' in file_path or 'shadow' in Path(file_path).name:
                return self._extract_from_shadow(content)
            elif 'pwdump' in Path(file_path).name.lower():
                return self._extract_from_pwdump(content)
            else:
                return self.extract_from_text(content)
                
        except Exception as e:
            self.logger.error(f"Error reading file {file_path}: {e}")
            return {}
    
    def _extract_from_shadow(self, content: str) -> Dict[str, List[str]]:
        """Extract hashes from /etc/shadow format."""
        hashes = {'shadow': []}
        
        for line in content.split('\n'):
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            
            parts = line.split(':')
            if len(parts) >= 2 and parts[1] and parts[1] != '*' and parts[1] != '!':
                hash_field = parts[1]
                
                # Parse different shadow hash formats
                if hash_field.startswith('$1$'):  # MD5
                    hashes.setdefault('md5_crypt', []).append(hash_field)
                elif hash_field.startswith('$5$'):  # SHA-256
                    hashes.setdefault('sha256_crypt', []).append(hash_field)
                elif hash_field.startswith('$6$'):  # SHA-512
                    hashes.setdefault('sha512_crypt', []).append(hash_field)
                elif hash_field.startswith('$y$'):  # yescrypt
                    hashes.setdefault('yescrypt', []).append(hash_field)
                else:
                    hashes['shadow'].append(hash_field)
        
        # Remove empty lists
        return {k: v for k, v in hashes.items() if v}
    
    def _extract_from_pwdump(self, content: str) -> Dict[str, List[str]]:
        """Extract hashes from pwdump format."""
        hashes = {'ntlm': [], 'lm': []}
        
        for line in content.split('\n'):
            line = line.strip()
            if not line:
                continue
            
            # pwdump format: username:uid:lm_hash:ntlm_hash:::
            parts = line.split(':')
            if len(parts) >= 4:
                lm_hash = parts[2]
                ntlm_hash = parts[3]
                
                if lm_hash and lm_hash != 'aad3b435b51404eeaad3b435b51404ee':
                    hashes['lm'].append(lm_hash)
                
                if ntlm_hash and ntlm_hash != '31d6cfe0d16ae931b73c59d7e0c089c0':
                    hashes['ntlm'].append(ntlm_hash)
        
        # Remove empty lists
        return {k: v for k, v in hashes.items() if v}
    
    def _extract_from_zip(self, file_path: str) -> Dict[str, List[str]]:
        """Extract hashes from ZIP files."""
        try:
            zip_hashes = extract_zip_hashes(file_path)
            if zip_hashes:
                return {'zip': list(zip_hashes.values())}
            return {}
        except Exception as e:
            self.logger.error(f"Error extracting from ZIP {file_path}: {e}")
            return {}
    
    def _extract_from_rar(self, file_path: str) -> Dict[str, List[str]]:
        """Extract hashes from RAR files."""
        try:
            # Check if rarfile is available
            import rarfile
            
            with rarfile.RarFile(file_path) as rf:
                # Check for password-protected files
                for info in rf.infolist():
                    if info.needs_password():
                        # For now, just indicate that RAR extraction is needed
                        # Full RAR hash extraction would require more complex implementation
                        self.logger.info(f"Found password-protected RAR file: {info.filename}")
                        return {'rar': [f"RAR:{file_path}:{info.filename}"]}
            
            return {}
            
        except ImportError:
            self.logger.warning("rarfile library not available for RAR extraction")
            return {}
        except Exception as e:
            self.logger.error(f"Error extracting from RAR {file_path}: {e}")
            return {}


class HashIdentifier:
    """Utility class for identifying hash types."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def identify_hash(self, hash_string: str) -> List[str]:
        """
        Identify possible hash types for a given hash string.
        
        Args:
            hash_string: The hash string to identify
            
        Returns:
            List of possible hash types
        """
        if not hash_string or not isinstance(hash_string, str):
            return []
        
        clean_hash = hash_string.strip()
        possible_types = []
        
        # Check for specific formats first
        if clean_hash.startswith('$2'):
            possible_types.append('bcrypt')
        elif clean_hash.startswith('*') and len(clean_hash) == 41:
            possible_types.append('mysql')
        elif clean_hash.startswith('$zip$'):
            possible_types.append('zip')
        elif clean_hash.startswith('$1$'):
            possible_types.append('md5_crypt')
        elif clean_hash.startswith('$5$'):
            possible_types.append('sha256_crypt')
        elif clean_hash.startswith('$6$'):
            possible_types.append('sha512_crypt')
        elif ':' in clean_hash:
            # Salted hash
            hash_part = clean_hash.split(':')[0]
            if len(hash_part) == 32:
                possible_types.append('md5_salt')
            elif len(hash_part) == 40:
                possible_types.append('sha1_salt')
        else:
            # Check by length for unsalted hashes
            clean_hex = clean_hash.lower()
            if re.match(r'^[a-f0-9]+$', clean_hex):
                length = len(clean_hex)
                if length == 32:
                    possible_types.extend(['md5', 'ntlm'])
                elif length == 40:
                    possible_types.append('sha1')
                elif length == 64:
                    possible_types.append('sha256')
                elif length == 128:
                    possible_types.append('sha512')
        
        return possible_types
    
    def identify_multiple_hashes(self, hashes: List[str]) -> Dict[str, List[str]]:
        """
        Identify hash types for multiple hashes.
        
        Args:
            hashes: List of hash strings
            
        Returns:
            Dictionary mapping hash types to lists of hashes
        """
        categorized = {}
        
        for hash_string in hashes:
            types = self.identify_hash(hash_string)
            
            # If multiple types are possible, use context or default to most common
            if len(types) > 1:
                # For 32-character hashes, prefer MD5 over NTLM unless context suggests otherwise
                if 'md5' in types and 'ntlm' in types:
                    types = ['md5']
            
            # Add to appropriate category
            for hash_type in types:
                if hash_type not in categorized:
                    categorized[hash_type] = []
                categorized[hash_type].append(hash_string)
        
        return categorized
    
    def get_hash_info(self, hash_type: str) -> Dict[str, str]:
        """
        Get information about a hash type.
        
        Args:
            hash_type: The hash type
            
        Returns:
            Dictionary with hash type information
        """
        hash_info = {
            'md5': {
                'name': 'MD5',
                'description': 'MD5 (Message Digest 5) - 128-bit hash function',
                'length': '32 characters',
                'security': 'Cryptographically broken, fast to crack'
            },
            'sha1': {
                'name': 'SHA-1',
                'description': 'SHA-1 (Secure Hash Algorithm 1) - 160-bit hash function',
                'length': '40 characters',
                'security': 'Cryptographically weak, moderately fast to crack'
            },
            'sha256': {
                'name': 'SHA-256',
                'description': 'SHA-256 (Secure Hash Algorithm 256) - 256-bit hash function',
                'length': '64 characters',
                'security': 'Cryptographically secure, slower to crack'
            },
            'sha512': {
                'name': 'SHA-512',
                'description': 'SHA-512 (Secure Hash Algorithm 512) - 512-bit hash function',
                'length': '128 characters',
                'security': 'Cryptographically secure, slower to crack'
            },
            'ntlm': {
                'name': 'NTLM',
                'description': 'NTLM (NT LAN Manager) - Windows password hash',
                'length': '32 characters',
                'security': 'Weak, very fast to crack'
            },
            'bcrypt': {
                'name': 'bcrypt',
                'description': 'bcrypt - Adaptive hash function based on Blowfish cipher',
                'length': '60 characters',
                'security': 'Very secure, designed to be slow'
            },
            'mysql': {
                'name': 'MySQL',
                'description': 'MySQL 4.1+ password hash format',
                'length': '41 characters (starts with *)',
                'security': 'Weak, fast to crack'
            }
        }
        
        return hash_info.get(hash_type, {
            'name': hash_type.upper(),
            'description': f'{hash_type} hash format',
            'length': 'Variable',
            'security': 'Unknown'
        })
