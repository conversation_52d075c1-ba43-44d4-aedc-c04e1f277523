#!/usr/bin/env python3
"""
SimpleCrack - GUI Password Cracking Tool
Main application entry point

A modern, cross-platform password cracking tool with a graphical user interface
inspired by <PERSON>.

Author: SimpleCrack Development Team
License: MIT
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import our modules
try:
    from gui.main_window import SimpleCrackGUI
    from core.utils import setup_logging, check_dependencies
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Please ensure all dependencies are installed: pip install -r requirements.txt")
    sys.exit(1)


def show_legal_warning():
    """Display legal and ethical use warning dialog."""
    warning_text = """
⚠️ LEGAL AND ETHICAL USE WARNING ⚠️

This tool is designed for legitimate security testing purposes only.

By using SimpleCrack, you agree to:
• Only use this tool on systems you own or have explicit written permission to test
• Comply with all applicable local, state, and federal laws
• Use this tool responsibly and ethically
• Not use this tool for malicious purposes or unauthorized access

The developers of SimpleCrack are not responsible for any misuse of this software.

UNAUTHORIZED ACCESS TO COMPUTER SYSTEMS IS ILLEGAL AND UNETHICAL.

Do you understand and agree to these terms?
"""
    
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    result = messagebox.askyesno(
        "Legal and Ethical Use Agreement",
        warning_text,
        icon='warning'
    )
    
    root.destroy()
    return result


def main():
    """Main application entry point."""
    # Set up logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Starting SimpleCrack application")
    
    # Show legal warning
    if not show_legal_warning():
        logger.info("User declined legal agreement, exiting")
        print("Application terminated by user.")
        sys.exit(0)
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        error_msg = f"Missing dependencies: {', '.join(missing_deps)}\n"
        error_msg += "Please install them using: pip install -r requirements.txt"
        messagebox.showerror("Missing Dependencies", error_msg)
        logger.error(f"Missing dependencies: {missing_deps}")
        sys.exit(1)
    
    try:
        # Create and run the main application
        logger.info("Initializing GUI")
        app = SimpleCrackGUI()
        
        logger.info("Starting main event loop")
        app.run()
        
    except Exception as e:
        logger.exception("Fatal error in main application")
        messagebox.showerror(
            "Fatal Error",
            f"A fatal error occurred:\n{str(e)}\n\nCheck the log files for more details."
        )
        sys.exit(1)
    
    logger.info("SimpleCrack application terminated normally")


if __name__ == "__main__":
    main()
